import { service } from './service'

import { config } from './config'

const { default_headers } = config

const request = (option: any) => {
  const { headersType, headers, ...otherOption } = option
  return service({
    ...otherOption,
    headers: {
      'Content-Type': headersType || default_headers,
      ...headers
    }
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  post: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  },
  epupload: async <T = any>(option: any) => {
    const { url, data, ...rest } = option;
    // 对于文件导出，通常应该使用GET请求
    // 如果必须用POST，确保参数在请求体中
    const res = await request({
      method: 'POST',
      url: url.split('?')[0], // 移除查询参数
      data: data || getQueryParams(url), // 将URL参数移到请求体
      headers: {
        'Content-Type': 'multipart/form-data',
        ...option.headers
      },
      responseType: 'blob', // 重要：告诉浏览器期待二进制响应
      ...rest
    });
    return res as unknown as Promise<T>;
  }
}
// 辅助函数：从URL提取查询参数
function getQueryParams(url: string) {
  const query = url.split('?')[1];
  if (!query) return {};
  return query.split('&').reduce((acc, pair) => {
    const [key, value] = pair.split('=');
    acc[key] = decodeURIComponent(value);
    return acc;
  }, {});
}
