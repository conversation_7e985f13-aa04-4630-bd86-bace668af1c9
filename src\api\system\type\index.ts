import request from '@/config/axios'

export interface TenantVO {
  id: number
  name: string
  contactName: string
  contactMobile: string
  status: number
  domain: string
  packageId: number
  username: string
  password: string
  expireTime: Date
  accountCount: number
  createTime: Date
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 类型管理列表
export const getTypePage = (params: TenantPageReqVO) => {
  return request.get({ url: '/system/type/page', params })
}
// 类型管理新增
export const addTypePage = (data: TenantVO) => {
  return request.post({ url: '/system/type/create', data })
}

// 类型管理更新
export const updateTypePage = (data: TenantVO) => {
  return request.put({ url: '/system/type/update', data })
}

// 类型管理删除
export const deleteType = (id: number) => {
  return request.delete({ url: '/system/type/delete?id=' + id })
}


// 查询租户详情
export const getTenant = (id: number) => {
  return request.get({ url: '/admin-api/system/type/page' + id })
}

// 新增租户
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/system/tenant/create', data })
}

// 修改租户
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除租户
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出租户
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}
