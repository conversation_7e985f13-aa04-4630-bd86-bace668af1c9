<template>
  <el-form
    :model="form"
    :rules="rules"
    ref="formRef"
    :disabled="disabled"
    label-width="200px"
    class="space-y-4"
  >
    <!-- 网管中心卫生情况 -->
    <el-form-item label="网管中心卫生情况" prop="hygienicConditions" required>
      <el-input
        v-model="form.hygienicConditions"
        type="textarea"
        placeholder="请输入网管中心卫生情况"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 各平台运行是否正常 -->
    <el-form-item label="各平台运行是否正常" required>
      <div class="space-y-2">
        <el-form-item
          v-for="(item, index) in form.platformState"
          :key="item.code"
          label-width="250px"
          :label="`(${index + 1}) ${item.name}`"
          :prop="`platformState[${index}].value`"
          :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]"
        >
          <el-radio-group v-model="item.value" :disabled="disabled">
            <el-radio :label="0">正常</el-radio>
            <el-radio :label="1">异常</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form-item>

    <!-- 异常描述 -->
    <el-form-item label="异常说明" prop="exceptDesc" v-if="hasAbnormalities" required>
      <el-input
        v-model="form.exceptDesc"
        type="textarea"
        placeholder="请输入异常说明"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 前端描述 -->
    <el-form-item label="前端设备故障情况" prop="frontDesc" required>
      <el-input
        v-model="form.frontDesc"
        type="textarea"
        placeholder="请输入前端设备故障情况"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 载波描述 -->
    <el-form-item label="运营商链路情况	" prop="carrierDesc" required>
      <el-input
        v-model="form.carrierDesc"
        type="textarea"
        placeholder="请输入运营商链路情况	"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 手持描述 -->
    <el-form-item label="故障处理情况	" prop="handDesc" required>
      <el-input
        v-model="form.handDesc"
        type="textarea"
        placeholder="请输入故障处理情况"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 其他描述 -->
    <el-form-item label="需要处理事项	" prop="otherDesc" required>
      <el-input
        v-model="form.otherDesc"
        type="textarea"
        placeholder="请输入需要处理事项"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 处理描述 -->
    <el-form-item label="跟进处理结果" prop="dealDesc" required>
      <el-input
        v-model="form.dealDesc"
        type="textarea"
        placeholder="请输入跟进处理结果"
        :disabled="disabled"
      />
    </el-form-item>

    <!-- 处理结果 -->
    <el-form-item label="处理结果" prop="dealResult" required>
      <el-input
        v-model="form.dealResult"
        type="textarea"
        placeholder="请输入处理结果"
        :disabled="disabled"
      />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'ExamForm' })

interface PlatformState {
  code: number
  name: string
  value: number
}

interface ShiftDetail {
  platformState: PlatformState[]
  exceptDesc: string
  frontDesc: string
  carrierDesc: string
  handDesc: string
  otherDesc: string
  dealDesc: string
  dealResult: string
  hygienicConditions: string
}

const props = defineProps<{
  form: ShiftDetail
  disabled?: boolean
}>()

const form = computed(() => props.form)
const disabled = computed(() => props.disabled ?? false)

// 检查是否存在异常状态
const hasAbnormalities = computed(() => form.value.platformState.some((item) => item.value === 1))

// 验证表单方法
const formRef = ref<FormInstance>()

const rules = ref<FormRules>({
  hygienicConditions: [{ required: true, message: '请输入网管中心卫生情况', trigger: 'blur' }],
  frontDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  carrierDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  handDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  otherDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  dealDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  dealResult: [{ required: true, message: '请输入结果', trigger: 'blur' }],
  exceptDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  platformState: {
    type: 'array',
    required: true,
    validator: (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('所有平台状态均需选择'))
      } else {
        const hasEmpty = value.some(
          (item: PlatformState) => item.value === undefined || item.value === null
        )
        if (hasEmpty) {
          callback(new Error('所有平台状态均需选择'))
        } else {
          callback()
        }
      }
    },
    trigger: 'change'
  }
})

const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    ElMessage.error(`请完善必填项`)
    return false
  }
}

defineExpose({ validateForm })
</script>

<style scoped>
/* 这里可以添加您的样式 */
</style>
