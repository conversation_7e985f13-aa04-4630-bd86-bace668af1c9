import request from '@/config/axios'
// 分页搜索
export const getWarnList = async (params) => {
  return await request.get({ url: `/infra/warn-label/page`, params })
}
// 详情底部
export const getDetailBottom = async (params) => {
  return await request.get({ url: '/infra/warn-source-param/list', params })
}
// 新增/编辑页面 的告警来源下拉框 获得告警源管理表（目前写死4个）不分页
export const getSelect = async () => {
  return await request.get({ url: '/infra/warn-source/list' })
}
// 新增的分页搜索
export const getSearchData = async (params) => {
  return await request.get({ url: '/infra/warn-source-param/page', params })
}
// 创建告警标签配置
export const createWarnLabel = async (data) => {
  return await request.post({ url: '/infra/warn-label/create', data })
}
// 更新 修改告警标签配置
export const updateWarnLabel = async (data) => {
  return await request.put({ url: '/infra/warn-label/update', data })
}
// 删除告警标签
export const deleteWarnLabel = async (params) => {
  return await request.delete({ url: '/infra/warn-label/delete', params })
}
