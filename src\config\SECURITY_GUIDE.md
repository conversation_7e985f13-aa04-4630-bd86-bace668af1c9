# API密钥安全配置指南

## 当前配置状态

✅ **已完成安全配置**：您的高德地图API密钥已安全配置到环境变量中

## 配置详情

### 1. 环境变量配置

您的API密钥已配置在 `.env` 文件中：

```bash
# 高德地图API密钥
VITE_AMAP_KEY = 4c9dc5244797d67aa9324a19c0ddfed6
```

### 2. 安全优势

- ✅ **避免代码暴露**：密钥不会出现在源代码中
- ✅ **环境隔离**：不同环境可以使用不同的密钥
- ✅ **版本控制安全**：密钥不会被提交到Git仓库
- ✅ **团队协作**：团队成员可以使用各自的密钥

### 3. 多环境配置

您可以为不同环境配置不同的密钥：

```bash
# .env (通用配置)
VITE_AMAP_KEY = 4c9dc5244797d67aa9324a19c0ddfed6

# .env.local (本地开发，优先级更高)
VITE_AMAP_KEY = your-local-dev-key

# .env.production (生产环境)
VITE_AMAP_KEY = your-production-key

# .env.test (测试环境)
VITE_AMAP_KEY = your-test-key
```

### 4. 使用方式

组件会自动从环境变量获取密钥：

```vue
<template>
  <!-- 无需传入密钥，自动从环境变量获取 -->
  <AMapSelector v-model="location" />
</template>
```

如果需要动态传入密钥（不推荐），也可以：

```vue
<template>
  <!-- 手动传入密钥（不推荐） -->
  <AMapSelector v-model="location" :amap-key="customKey" />
</template>
```

## 安全建议

### ✅ 推荐做法

1. **使用环境变量**：将所有敏感信息配置到环境变量中
2. **添加到.gitignore**：确保 `.env.local` 文件不被提交
3. **定期更换密钥**：定期更换API密钥以提高安全性
4. **权限控制**：在高德控制台设置IP白名单和域名限制

### ❌ 避免做法

1. **硬编码密钥**：不要将密钥直接写在代码中
2. **提交敏感文件**：不要将包含密钥的文件提交到版本控制
3. **公开分享**：不要在公开场合分享包含密钥的代码
4. **使用生产密钥开发**：开发环境不要使用生产环境的密钥

## 配置验证

组件提供了密钥验证功能：

```typescript
import { validateAmapKey, getAmapKey } from '@/config/map'

// 验证密钥是否配置
if (validateAmapKey()) {
  console.log('密钥配置正确')
} else {
  console.error('密钥未配置或无效')
}

// 获取密钥（会自动验证）
try {
  const key = getAmapKey()
  console.log('密钥获取成功')
} catch (error) {
  console.error('密钥获取失败:', error.message)
}
```

## 故障排除

### 问题1：组件提示密钥未配置

**解决方案**：
1. 检查 `.env` 文件中是否正确配置了 `VITE_AMAP_KEY`
2. 重启开发服务器使环境变量生效
3. 确保环境变量名称正确（必须以 `VITE_` 开头）

### 问题2：地图无法加载

**解决方案**：
1. 检查密钥是否有效
2. 确认密钥对应的应用类型为 "Web端(JS API)"
3. 检查控制台是否有相关错误信息
4. 验证网络连接是否正常

### 问题3：不同环境使用不同密钥

**解决方案**：
1. 在对应的环境文件中配置密钥（如 `.env.production`）
2. 使用构建工具的环境变量功能
3. 在部署时设置环境变量

## 最佳实践

1. **开发环境**：使用 `.env.local` 配置个人开发密钥
2. **测试环境**：使用专门的测试密钥，限制调用次数
3. **生产环境**：使用生产密钥，设置严格的安全限制
4. **监控使用**：定期检查API调用量和异常情况

## 总结

您的高德地图API密钥已经按照最佳安全实践进行配置：

- ✅ 配置到环境变量中
- ✅ 避免代码暴露
- ✅ 支持多环境配置
- ✅ 提供验证和错误处理

现在您可以安全地使用 `AMapSelector` 组件，无需担心密钥安全问题！
