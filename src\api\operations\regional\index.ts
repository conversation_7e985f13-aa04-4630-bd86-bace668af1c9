import request from '@/config/axios'
export interface GetDicByDictTypeRes {
  code: number
  data: {
    name: Record<string, unknown>
    id: number
    parentId: number
    config: {
      idKey: string
      parentIdKey: string
      weightKey: string
      nameKey: string
      childrenKey: string
      deep: number
    }
    weight: Record<string, unknown>
    empty: boolean
  }[]
  msg: string
}
// 分页/搜索  {dictType:oamArea}
export const getList = async (params) => {
  return await request.get({ url: `/infra/device-dict/getDicByDictType`, params })
}

// 添加/新增
export const addList = async (data) => {
  return await request.post({ url: `/infra/device-dict/create`, data })
}
// 编辑/修改
export const updateItem = async (data) => {
  return await request.put({ url: `/infra/device-dict/update`, data })
}

// 删除
export const deleteList = async (params) => {
  return await request.delete({ url: `/infra/device-dict/delete`, params })
}
