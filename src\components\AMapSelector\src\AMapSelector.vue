<template>
  <div class="amap-selector">
    <!-- 输入框显示 -->
    <el-input
      v-model="displayValue"
      :placeholder="placeholder"
      readonly
      @click="openDialog"
      class="amap-input"
    >
      <template #suffix>
        <el-icon class="cursor-pointer">
          <Location />
        </el-icon>
      </template>
    </el-input>

    <!-- 地图选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :width="dialogWidth"
      :destroy-on-close="true"
      @close="handleDialogClose"
    >
      <div class="amap-dialog-content">
        <!-- 搜索框 -->
        <div class="search-container mb-4">
          <el-autocomplete
            v-model="searchKeyword"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入地址进行搜索"
            class="w-full"
            :trigger-on-focus="false"
            @select="handleSearchSelect"
            clearable
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
            <template #default="{ item }">
              <div class="search-item">
                <div class="search-name">{{ item.name }}</div>
                <div class="search-address text-gray-500 text-sm">{{ item.district }}{{ item.address }}</div>
              </div>
            </template>
          </el-autocomplete>
        </div>

        <!-- 地图容器 -->
        <div
          ref="mapContainer"
          class="amap-container"
          :style="{ height: mapHeight }"
        ></div>

        <!-- 选中位置信息显示 -->
        <div v-if="selectedLocation.address" class="selected-info mt-4 p-3 bg-gray-50 rounded">
          <div class="info-row">
            <span class="label">地址：</span>
            <span class="value">{{ selectedLocation.address }}</span>
          </div>
          <div class="info-row">
            <span class="label">经纬度：</span>
            <span class="value">{{ selectedLocation.lng }}, {{ selectedLocation.lat }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleConfirm" :disabled="!selectedLocation.lng">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onUnmounted } from 'vue'
import { Location, Search } from '@element-plus/icons-vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { amapConfig, getAmapKey } from '@/config/map'
import type {
  LocationData,
  SearchResult,
  AMapSelectorProps,
  AMapSelectorEmits,
  MapClickEvent,
  MarkerDragEvent
} from '../types'

// Props定义
const props = withDefaults(
  defineProps<AMapSelectorProps>(),
  {
    placeholder: '请选择位置',
    dialogTitle: '选择位置',
    dialogWidth: '800px',
    mapHeight: '400px',
    amapKey: '', // 如果不传入，将使用环境变量中的密钥
    defaultCenter: () => amapConfig.defaultOptions.center,
    defaultZoom: amapConfig.defaultOptions.zoom,
    modelValue: () => ({ lng: 0, lat: 0, address: '' })
  }
)

// Emits定义
const emit = defineEmits<AMapSelectorEmits>()

// 响应式数据
const dialogVisible = ref(false)
const mapContainer = ref<HTMLDivElement>()
const searchKeyword = ref('')
const selectedLocation = ref<LocationData>({
  lng: 0,
  lat: 0,
  address: ''
})

let map: any = null
let marker: any = null
let geocoder: any = null
let autoComplete: any = null

// 计算属性
const displayValue = computed(() => {
  return props.modelValue.address || ''
})

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true
  nextTick(() => {
    initMap()
  })
}

// 初始化地图
const initMap = async () => {
  try {
    // 检查容器是否存在
    if (!mapContainer.value) {
      console.error('地图容器未找到')
      return
    }

    // 获取API密钥：优先使用props传入的，否则使用环境变量
    const apiKey = props.amapKey || getAmapKey()

    const AMap = await AMapLoader.load({
      key: apiKey,
      version: amapConfig.version,
      plugins: amapConfig.plugins
    })

    // 创建地图实例
    map = new AMap.Map(mapContainer.value, {
      zoom: props.defaultZoom,
      center: props.modelValue.lng ? [props.modelValue.lng, props.modelValue.lat] : props.defaultCenter,
      mapStyle: amapConfig.defaultOptions.mapStyle
    })

    // 初始化地理编码
    geocoder = new AMap.Geocoder()

    // 初始化自动完成
    autoComplete = new AMap.AutoComplete({
      city: '全国'
    })

    // 如果有初始值，添加标记
    if (props.modelValue.lng) {
      addMarker([props.modelValue.lng, props.modelValue.lat])
      selectedLocation.value = { ...props.modelValue }
    }

    // 地图点击事件
    map.on('click', handleMapClick)

  } catch (error) {
    console.error('地图初始化失败:', error)
    // 可以在这里添加用户友好的错误提示
    // ElMessage.error('地图加载失败，请检查网络连接或API密钥配置')
  }
}

// 地图点击处理
const handleMapClick = (e: MapClickEvent) => {
  const { lng, lat } = e.lnglat
  addMarker([lng, lat])

  // 逆地理编码获取地址
  geocoder.getAddress([lng, lat], (status: string, result: any) => {
    if (status === 'complete' && result.regeocode) {
      const address = result.regeocode.formattedAddress
      selectedLocation.value = {
        lng,
        lat,
        address
      }
    } else {
      selectedLocation.value = {
        lng,
        lat,
        address: `${lng.toFixed(6)}, ${lat.toFixed(6)}`
      }
    }
  })
}

// 添加标记
const addMarker = (position: [number, number]) => {
  if (marker) {
    marker.setPosition(position)
  } else {
    marker = new (window as any).AMap.Marker({
      position,
      draggable: true
    })
    map.add(marker)

    // 标记拖拽事件
    marker.on('dragend', (e: MarkerDragEvent) => {
      const { lng, lat } = e.lnglat
      handleMapClick({ lnglat: { lng, lat } })
    })
  }

  map.setCenter(position)
}

// 搜索建议
const querySearchAsync = (queryString: string, callback: (results: any[]) => void) => {
  if (!queryString.trim()) {
    callback([])
    return
  }

  autoComplete.search(queryString, (status: string, result: any) => {
    if (status === 'complete' && result.tips) {
      const suggestions = result.tips
        .filter((tip: any) => tip.location)
        .map((tip: any) => ({
          name: tip.name,
          address: tip.address,
          district: tip.district,
          location: [tip.location.lng, tip.location.lat],
          value: tip.name
        }))
      callback(suggestions)
    } else {
      callback([])
    }
  })
}

// 搜索选择处理
const handleSearchSelect = (item: SearchResult) => {
  if (!item || !item.location || item.location.length < 2) {
    console.error('搜索结果数据无效:', item)
    return
  }

  const [lng, lat] = item.location
  addMarker([lng, lat])
  selectedLocation.value = {
    lng,
    lat,
    address: `${item.district || ''}${item.address || ''}`,
    name: item.name || ''
  }
  searchKeyword.value = ''
}

// 确认选择
const handleConfirm = () => {
  if (selectedLocation.value.lng) {
    emit('update:modelValue', selectedLocation.value)
    emit('change', selectedLocation.value)
    dialogVisible.value = false
  }
}

// 取消选择
const handleCancel = () => {
  dialogVisible.value = false
}

// 重置选择
const handleReset = () => {
  selectedLocation.value = {
    lng: 0,
    lat: 0,
    address: ''
  }
  searchKeyword.value = ''
  if (marker) {
    map.remove(marker)
    marker = null
  }
  map.setCenter(props.defaultCenter)
  map.setZoom(props.defaultZoom)
}

// 弹窗关闭处理
const handleDialogClose = () => {
  // 清理地图资源
  if (map) {
    map.destroy()
    map = null
    marker = null
  }
}

// 组件卸载时清理
onUnmounted(() => {
  if (map) {
    map.destroy()
  }
})
</script>

<style scoped lang="scss">
.amap-selector {
  .amap-input {
    cursor: pointer;

    :deep(.el-input__inner) {
      cursor: pointer;
    }
  }
}

.amap-dialog-content {
  .search-container {
    .search-item {
      .search-name {
        font-weight: 500;
      }

      .search-address {
        margin-top: 2px;
      }
    }
  }

  .amap-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .selected-info {
    .info-row {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 500;
        min-width: 80px;
        color: #606266;
      }

      .value {
        flex: 1;
        color: #303133;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
