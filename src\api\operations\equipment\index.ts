import request from '@/config/axios'
export interface BannerVO {
  projectCode: string
  projectName: string
  projectType: number
  oamType: number
  warnType: number
  startDate: string
  endDate: string
  projectAmount: number
}
// 分页/搜索
export const getList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/page`, data })
}
// 设备总数/设备完好率……
export const postOverview = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/device/statistics/search`, data })
}
// 告警总数/当日新增……
export const getWarn = async (params) => {
  return await request.get({ url: `/infra/warn-assets/getWarnCountByParams`, params })
}
// 详情
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/device-overview-info/get`, params })
}

// 添加/新增
export const addList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/create`, data })
}
// 编辑/修改
export const updateItem = async (data) => {
  return await request.put({ url: `/infra/device-overview-info/update`, data })
}

// 删除
export const deleteList = async (params) => {
  return await request.delete({ url: `/infra/device-overview-info/delete`, params })
}
// 同步
export const synchronous = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/sync`, data })
}
// 导入
export const importList = async (data) => {
  return await request.upload({ url: `/infra/device-overview-info/import/template`, data })
}
// 导出
export const exportList = async (params) => {
  return await request.download({ url: `/infra/device-overview-info/export-excel`, params })
}
// 获得字典列表(设备来源)
export const getEquipmentSource = async (params) => {
  return await request.get({ url: `/infra/device-dict/dict/type/list`, params })
}
// 获得日志
export const getLog = async (params) => {
  return await request.get({ url: `/system/operate-log/page`, params })
}
