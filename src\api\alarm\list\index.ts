import request from '@/config/axios'
// 告警标签
export const getWarnLabel = async () => {
  return await request.get({ url: `/infra/warn-label/list` })
}
// 告警来源
export const getWarnSource = async () => {
  return await request.get({ url: `/infra/warn-source/list` })
}
// 高级搜索 所属项目下拉
export const getBelongsProject = async () => {
  return await request.get({ url: `/infra/oam-project/list` })
}
// 资产总览不分页 (第一个接口)
export const postListNoPage = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/list`, data })
}
// 设备告警分页 (第二个接口)
export const getWarnList = async (params) => {
  return await request.get({ url: `/infra/warn-assets/page`, params })
}
// 详情-左侧 设备告警
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/warn-assets/get`, params })
}
// 创建 添加白名单
export const postAddWhiteList = async (data) => {
  return await request.post({ url: `/infra/warn-white-list/create`, data })
}
// 关闭告警  /infra/warn-assets/close
export const deleteWarn = async (params) => {
  return await request.delete({ url: `/infra/warn-assets/close`, params })
}
// 导出
export const exportWarnList = async (params) => {
  return await request.download({ url: `/infra/warn-assets/export-excel`, params })
}

// export const addList = async (data) => {
//   return await request.post({ url: `/infra/oam-project/create`, data })
// }
