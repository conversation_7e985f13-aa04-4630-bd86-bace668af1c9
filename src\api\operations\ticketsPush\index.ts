import request from '@/config/axios'

export interface BannerVO {
  projectCode: string
  projectName: string
  projectType: number
  oamType: number
  warnType: number
  startDate: string
  endDate: string
  projectAmount: number
}
// 根据单位类型查数据获取id传字符串
export const searchUnit = async (params) => {
  return await request.get({ url: `/system/type/page`, params })
}
// /system/dept/list/oprMainUnit?pageNo=1&pageSize=100
// 获得左侧单位列表
export const getUnitList = async (params) => {
  return await request.get({ url: `/system/dept/list/oprMainUnit`, params })
}
// 获得设备类型
export const getType = async () => {
  return await request.get({ url: `/infra/device-type/getDicTree` })
}

// 分页/搜索
export const getList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/page`, data })
}
// 管理后台-资产总览 获得资产总览分页/不分页
// 右侧表格数据获取
// /infra/device-overview-info/list
// 不分页/搜索 对话框内 data为空{}
export const getNoPageList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/list`, data })
}
// 详情
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/device-overview-info/get`, params })
}

// 添加/左侧列表数据
export const getDialogLeft = async () => {
  return await request.get({ url: `/infra/oam-project/list` })
}
// 添加/右侧列表数据
export const getDialogRight = async (params) => {
  return await request.get({ url: `/system/device-permission-info/dept/get`, params })
}

// 弹窗确定更新资产单位id/移出不传deptID可以更新为null
export const updateItem = async (data) => {
  return await request.put({ url: `/infra/device-overview-info/updateUnitByCode`, data })
}

// 导入
export const importEquipmentSingle = async (data) => {
  return await request.upload({ url: `/infra/device-overview-info/importSetUnit`, data })
}
// 导出模版
export const exportSingleStencil = async () => {
  return await request.download({ url: `/infra/device-overview-info/importSetUnit/template` })
}
// 导出
export const exportList = async (params) => {
  return await request.download({ url: `/infra/device-overview-info/export-excel`, params })
}
// 获得日志
export const getLog = async (params) => {
  return await request.get({ url: `/system/operate-log/page`, params })
}
// 获取视频路径
export const getVideo = async (params) => {
  return await request.get({ url: `infra/assets-hik/getCameraPreview?assetsCode=${params.assetsCode}&protocol=${params.protocol}`})
}
