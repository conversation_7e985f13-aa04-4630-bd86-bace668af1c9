import request from '@/config/axios'
// 分页搜索
export const getWarnWhiteList = async (params) => {
  return await request.get({ url: `/infra/warn-white-list/page`, params })
}
// 新增
export const postAddWhite = async (data) => {
  return await request.post({ url: '/infra/warn-white-list/create', data })
}
// 删除
export const deleteWarnWhite = async (params) => {
  return await request.delete({ url: '/infra/warn-white-list/delete', params })
}

// 导入
export const importWarnWhite = async (data) => {
  return await request.upload({ url: `/infra/warn-white-list/import-warn-white`, data })
}
// 导出
export const exportWarnWhite = async (params) => {
  return await request.download({ url: `/infra/warn-white-list/export-excel`, params })
}

// 获得导入模版
export const exportWarnWhiteTemplate = async () => {
  return await request.download({ url: `/infra/warn-white-list/get-import-template` })
}
// 详情左侧数据
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/device-overview-info/getByCode`, params })
}
