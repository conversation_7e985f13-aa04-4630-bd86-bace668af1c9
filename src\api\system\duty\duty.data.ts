import request from '@/config/axios'

// 交接人员
export const getDutyStaffList = () => {
  return request.get({ url: '/system/duty-staff/list' })
}
export const getDutyStaffDelete = (id: Number) => {
  return request.delete({ url: `/system/duty-staff/delete?id=${id}` })
}
export const getDutyStaffCreate = (data: any) => {
  return request.post({ url: `/system/duty-staff/create`, data: data })
}

//交接班
export const getDutyTimeList = () => {
  return request.get({ url: '/system/duty-time/list' })
}
export const getDutyTimeCreate = (data: any) => {
  return request.post({ url: '/system/duty-time/create', data })
}
export const getDutyTimeUpdate = (data: any) => {
  return request.put({ url: '/system/duty-time/update', data })
}
export const getDutyTimeDelete = (groupId: number) => {
  return request.delete({ url: `/system/duty-time/delete?groupId=${groupId}` })
}

// 设置默认
export const getDutyTimeUpdateDefault = (groupId: number) => {
  return request.get({ url: `/system/duty-time/update/default?groupId=${groupId}` })
}
// 交接班详情
export const getDutyDeTailList = (params: any) => {
  return request.get({ url: `/system/duty-detail/list`, params })
}
export const getDutyDeTailUpdate = (data: any) => {
  return request.post({ url: `/system/duty-detail/create`, data })
}
export const getDutyRecordCreate = (data: any) => {
  return request.post({ url: `/system/duty-record/create`, data })
}
export const getUpdateDutyTime = (data: any) => {
  return request.post({ url: `/system/duty-detail/update`, data })
}

// 导出交接班详情
export const exportDuty = async (params: any) => {
  return request.download({ url: `/system/duty-detail/export-excel`, params })
}

//获得交接班-交接班记录(简要)

export const getDutyRecordListSimple = (params: any) => {
  return request.get({ url: `/system/duty-record/list-simple`, params })
}
export const getDutyRecordToday = () => {
  return request.get({ url: `/system/duty-record/get/today` })
}
export const getDutyRecordPlatformList = () => {
  return request.get({ url: `/system/duty-record/get/platform/list` })
}
export const getDutyRecordListDetail = (data: any) => {
  return request.post({ url: `/system/duty-record/list-detail`, data })
}
// 导出交接班-交接班记录
export const exportDutyRecord = async (params: any) => {
  return request.download({ url: `/system/duty-record/export-excel`, params })
}

export const getDutyRecordShift = (params: any) => {
  return request.get({ url: `/system/duty-record/get/detail/shift`, params })
}

export const getImportTemplate = () => {
  return request.download({ url: `/system/duty-detail/get-import-template` })
}
