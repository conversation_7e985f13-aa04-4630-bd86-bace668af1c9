import request from '@/config/axios'

export interface TenantVO {
  pageNo: number
  pageSize: number
  ids?: number[] // 根据实际情况判断是否为可选字段
  assetsCode?: string
  projectId?: number
  deptId?: number
  assetsTypeId?: number
  assetsSource?: string
  siteId?: number
  areaId?: number
  supplierId?: number
  modelId?: number
  groupId?: number
  warrantyDateStart?: string // 或者使用 Date 类型，取决于前后端交互方式
  warrantyDateEnd?: string   // 同上
  warrantyStatus?: number
  isKey?: number             // 或者使用 boolean 类型，取决于实际值
  labelId?: number
}

export interface Person {
  auxiliaryCode: string;
  auxiliaryName: string;
  areaId:string;
  assetsTypeId:string;
  longitude:string;
  latitude:string;
  picUrl?: string; // 图片 URL 可选
  deviceAssetsAuxiliarySaveReqVOList?: { id: any, deviceId: any }[]; // 可选，根据需求添加
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  dictType?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 获得设备类型列表
export const getDeviceType = (params: TenantPageReqVO) => {
  return request.get({ url: '/infra/device-type/getDicTree', params })
}
// 获得字典数据
export const getDicByDictType = (params) => {
  return request.get({ url: '/infra/device-dict/getDicByDictType?dictType='+ params })
}

// 获取资产分页
export const getPropertyPage = (data: TenantVO) => {
  return request.post({ url: '/infra/device-overview-info/page', data })
}
// 获取资产不分页
export const getPropertyPagination = (data: TenantVO) => {
  return request.post({ url: '/infra/device-overview-info/list', data })
}

// 资产删除
export const deleteProperty = (id: number) => {
  return request.delete({ url: '/infra/device-overview-info/delete?id=' + id })
}

// 获取辅件分页
export const getAccessoriesPage = (data: TenantVO) => {
  return request.post({ url: '/infra/device-auxiliary-info/page', data })
}

// 辅件删除
export const deleteAccessories = (id: number) => {
  return request.delete({ url: '/infra/device-auxiliary-info/delete?id=' + id })
}

// 辅件新增
export const AccessoriesAdd = (data: Person) => {
  return request.post({ url: '/infra/device-auxiliary-info/create', data })
}
// 辅件编辑
export const AccessoriesUpdate = (data: Person) => {
  return request.put({ url: '/infra/device-auxiliary-info/update', data })
}

// 辅件详情
export const AccDetails = (id: number) => {
  return request.get({ url: 'infra/device-auxiliary-info/get?id=' + id })
}
// 获取导入模板
export const getTemplate = (params) => {
  return request.download({ url: '/infra/device-overview-info/get/template' ,params })
}

// 导出接口
export const deviceExportexcel = (params:TenantVO) => {
  return request.download({ url: '/infra/device-overview-info/export-excel' ,params })
}
// 辅件接口
export const AccExportexcel = (params:TenantVO) => {
  return request.epupload({ url: '/infra/device-auxiliary-info/export-excel' ,params })
}

// 新增租户
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/system/tenant/create', data })
}

// 修改租户
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除租户
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出租户
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}
