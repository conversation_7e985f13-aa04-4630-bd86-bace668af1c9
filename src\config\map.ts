/**
 * 地图相关配置
 */

// 高德地图配置
export const amapConfig = {
  // 从环境变量获取API密钥
  key: import.meta.env.VITE_AMAP_KEY || '',
  
  // 高德地图版本
  version: '2.0',
  
  // 需要加载的插件
  plugins: [
    'AMap.Geocoder',      // 地理编码
    'AMap.AutoComplete',  // 自动完成
    'AMap.PlaceSearch'    // 地点搜索
  ],
  
  // 默认地图配置
  defaultOptions: {
    center: [120.088, 30.86], // 默认中心点（湖州）
    zoom: 12,                 // 默认缩放级别
    mapStyle: 'amap://styles/normal' // 地图样式
  }
}

// 验证API密钥是否配置
export const validateAmapKey = (): boolean => {
  if (!amapConfig.key) {
    console.error('高德地图API密钥未配置，请在环境变量中设置 VITE_AMAP_KEY')
    return false
  }
  return true
}

// 获取高德地图API密钥
export const getAmapKey = (): string => {
  if (!validateAmapKey()) {
    throw new Error('高德地图API密钥未配置')
  }
  return amapConfig.key
}

// 地图工具函数
export const mapUtils = {
  /**
   * 格式化坐标显示
   */
  formatCoordinate: (lng: number, lat: number, precision = 6): string => {
    return `${lng.toFixed(precision)}, ${lat.toFixed(precision)}`
  },
  
  /**
   * 验证坐标是否有效
   */
  isValidCoordinate: (lng: number, lat: number): boolean => {
    return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90
  },
  
  /**
   * 计算两点之间的距离（简单计算）
   */
  getDistance: (lng1: number, lat1: number, lng2: number, lat2: number): number => {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }
}
