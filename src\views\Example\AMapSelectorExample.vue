<template>
  <div class="amap-selector-example">
    <div class="example-container">
      <h2>高德地图选择器组件示例</h2>

      <div class="example-section">
        <h3>基础用法</h3>
        <div class="form-item">
          <label>选择位置：</label>
          <AMapSelector
            v-model="selectedLocation"
            placeholder="请选择位置"
            @change="handleLocationChange"
            :amap-key="amapKey"
          />
        </div>

        <div v-if="selectedLocation.address" class="result-display">
          <h4>选中的位置信息：</h4>
          <p><strong>地址：</strong>{{ selectedLocation.address }}</p>
          <p><strong>经度：</strong>{{ selectedLocation.lng }}</p>
          <p><strong>纬度：</strong>{{ selectedLocation.lat }}</p>
          <p v-if="selectedLocation.name"><strong>名称：</strong>{{ selectedLocation.name }}</p>
        </div>
      </div>

      <div class="example-section">
        <h3>自定义配置</h3>
        <div class="form-item">
          <label>自定义样式：</label>
          <AMapSelector
            v-model="customLocation"
            placeholder="自定义地图选择器"
            dialog-title="请选择您的位置"
            dialog-width="900px"
            map-height="500px"
            :default-center="[120.15, 30.28]"
            :default-zoom="14"
            :amap-key="amapKey"
          />
        </div>
      </div>

      <div class="example-section">
        <h3>表单中使用</h3>
        <el-form :model="formData" label-width="120px">
          <el-form-item label="公司名称：">
            <el-input v-model="formData.companyName" placeholder="请输入公司名称" />
          </el-form-item>

          <el-form-item label="公司地址：">
            <AMapSelector
              v-model="formData.location"
              placeholder="请选择公司地址"
              :amap-key="amapKey"
            />
          </el-form-item>

          <el-form-item label="联系电话：">
            <el-input v-model="formData.phone" placeholder="请输入联系电话" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="example-section">
        <h3>使用说明</h3>
        <div class="usage-info">
          <h4>组件特性：</h4>
          <ul>
            <li>支持点击输入框打开地图选择弹窗</li>
            <li>支持地址搜索和自动补全</li>
            <li>支持地图点击选择位置</li>
            <li>支持标记拖拽调整位置</li>
            <li>支持逆地理编码获取详细地址</li>
            <li>支持自定义弹窗大小和地图高度</li>
          </ul>

          <h4>Props 参数：</h4>
          <ul>
            <li><code>modelValue</code>: 绑定值，包含 lng、lat、address 字段</li>
            <li><code>placeholder</code>: 输入框占位符</li>
            <li><code>dialogTitle</code>: 弹窗标题</li>
            <li><code>dialogWidth</code>: 弹窗宽度</li>
            <li><code>mapHeight</code>: 地图高度</li>
            <li><code>amapKey</code>: 高德地图API密钥</li>
            <li><code>defaultCenter</code>: 默认中心点坐标</li>
            <li><code>defaultZoom</code>: 默认缩放级别</li>
          </ul>

          <h4>Events 事件：</h4>
          <ul>
            <li><code>update:modelValue</code>: 值更新事件</li>
            <li><code>change</code>: 值改变事件</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getAmapKey } from '@/config/map'

// 高德地图API密钥 - 从环境变量获取
const amapKey = getAmapKey()

// 响应式数据
const selectedLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const customLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const formData = reactive({
  companyName: '',
  location: {
    lng: 0,
    lat: 0,
    address: ''
  },
  phone: ''
})

// 位置改变处理
const handleLocationChange = (location: any) => {
  console.log('位置已选择:', location)
  ElMessage.success(`已选择位置: ${location.address}`)
}

// 提交表单
const submitForm = () => {
  if (!formData.companyName) {
    ElMessage.warning('请输入公司名称')
    return
  }

  if (!formData.location.address) {
    ElMessage.warning('请选择公司地址')
    return
  }

  if (!formData.phone) {
    ElMessage.warning('请输入联系电话')
    return
  }

  console.log('表单数据:', formData)
  ElMessage.success('表单提交成功！')
}

// 重置表单
const resetForm = () => {
  formData.companyName = ''
  formData.location = {
    lng: 0,
    lat: 0,
    address: ''
  }
  formData.phone = ''
  ElMessage.info('表单已重置')
}
</script>

<style scoped lang="scss">
.amap-selector-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .example-container {
    h2 {
      color: #303133;
      margin-bottom: 30px;
      text-align: center;
    }

    .example-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      background: #fff;

      h3 {
        color: #409eff;
        margin-bottom: 20px;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .form-item {
        margin-bottom: 20px;

        label {
          display: inline-block;
          width: 120px;
          font-weight: 500;
          color: #606266;
        }
      }

      .result-display {
        margin-top: 20px;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;
        border-left: 4px solid #409eff;

        h4 {
          color: #303133;
          margin-bottom: 10px;
        }

        p {
          margin: 5px 0;
          color: #606266;

          strong {
            color: #303133;
          }
        }
      }

      .usage-info {
        h4 {
          color: #303133;
          margin: 20px 0 10px 0;
        }

        ul {
          margin: 10px 0;
          padding-left: 20px;

          li {
            margin: 8px 0;
            color: #606266;
            line-height: 1.6;

            code {
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 3px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              color: #e6a23c;
            }
          }
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
