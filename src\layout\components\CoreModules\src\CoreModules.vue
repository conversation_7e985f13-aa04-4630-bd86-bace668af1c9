<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { usePermissionStore } from '@/store/modules/permission'
import { Icon } from '@/components/Icon'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const permissionStore = usePermissionStore()
const scrollContainer = ref<HTMLElement | null>(null)

const menuRouters = computed(() => {
  return permissionStore.getRouters?.filter((v, i) => !v.meta?.hidden && i > 1) || []
})
const router = useRouter() // 路由
const route = useRoute() // 路由

const handleMenu = (item: AppRouteRecordRaw) => {
  let path = item.path
  if (item?.children && item?.children.length > 0 && item.path !== item.children[0].name) {
    path += getDeepestPath(item?.children || [])
  }
  router.push({ path })
  const children = unref(item.children) || []
  permissionStore.setChildRoutes(children)
}

function getDeepestPath(menu) {
  let fullPath = ''
  function traverse(node) {
    if (!node) return
    fullPath += '/' + node.path
    if (node.children && node.children.length > 0) {
      traverse(node.children[0])
    }
  }
  if (Array.isArray(menu) && menu.length > 0) {
    traverse(menu[0])
  }
  return fullPath.replace(/\/+/g, '/') // 清除多余的斜杠
}

// 计算开头 `/` 的路径部分
const pathWithoutSlash = computed(() => {
  const path = route.path.startsWith('/') ? route.path.slice(1) : route.path
  const firstDir = path.split('/')[0] // 第一级路径片段
  return firstDir
})

// 监听 pathWithoutSlash 变化
watch(
  pathWithoutSlash,
  (newValue) => {
    const children = unref(menuRouters).find((d) => d.path === `/${newValue}`)?.children || []
    permissionStore.setChildRoutes(children)
  },
  { immediate: true }
)

const scrollable = ref(false) // 是否需要滚动
const showLeftButton = ref(false) // 是否显示左侧按钮
const showRightButton = ref(false) // 是否显示右侧按钮

// 更新按钮显示状态
const updateButtons = () => {
  const container = scrollContainer.value
  if (!container) return
  scrollable.value = container.scrollWidth > container.clientWidth
  showLeftButton.value = container.scrollLeft > 0
  showRightButton.value = container.scrollLeft < container.scrollWidth - container.clientWidth
}

// 滚动到最左侧
const scrollToLeft = () => {
  const container = scrollContainer.value
  if (container) {
    container.scrollTo({ left: 0, behavior: 'smooth' })
  }
}

// 滚动到最右侧
const scrollToRight = () => {
  const container = scrollContainer.value
  if (container) {
    container.scrollTo({ left: container.scrollWidth, behavior: 'smooth' })
  }
}

// 监听滚动事件
onMounted(() => {
  nextTick(() => {
    updateButtons()
  })
  const container = scrollContainer.value
  if (container) {
    container.addEventListener('scroll', updateButtons)
  }
  window.addEventListener('resize', updateButtons)
})

onUnmounted(() => {
  const container = scrollContainer.value
  if (container) {
    container.removeEventListener('scroll', updateButtons)
  }
  window.removeEventListener('resize', updateButtons)
})
</script>

<template>
  <div class="relative">
    <!-- 左侧滚动按钮 -->
    <el-button
      v-if="scrollable && showLeftButton"
      class="scroll-btn left-btn"
      :icon="ArrowLeft"
      circle
      @click="scrollToLeft"
    />

    <!-- 主要滚动内容 -->
    <div ref="scrollContainer" class="overflow-x-auto whitespace-nowrap py-4">
      <div class="flex justify-start items-center space-x-6 px-4">
        <div
          v-for="(item, index) in menuRouters.filter((v) => v.meta.title !== '系统管理')"
          :key="index"
          @click="handleMenu(item)"
          class="core-menu flex items-center text-gray-500 cursor-pointer px-2 py-1"
          :class="route.path.includes(item.path) ? 'core-menu-active' : ''"
        >
          <Icon :icon="item.meta.icon" class="w-6 h-6 mr-1" svgClass="inline-block" />
          <span class="text-sm">{{ item.meta.title }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧滚动按钮 -->
    <el-button
      v-if="scrollable && showRightButton"
      class="scroll-btn right-btn"
      :icon="ArrowRight"
      circle
      @click="scrollToRight"
    />
  </div>
</template>

<style lang="scss" scoped>
/* 让滚动条在 macOS / Windows 上更美观 */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}
.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 滚动按钮基础样式 */
.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

/* 左侧按钮 */
.left-btn {
  left: -40px;
}

/* 右侧按钮 */
.right-btn {
  right: -40px;
}
.core-menu:hover {
  color: #0053df;
}
.core-menu-active {
  color: #0053df;
}
</style>
