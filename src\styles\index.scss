@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.el-table thead th {
  background-color: #f4f6f8 !important;
  font-size: 14px !important;
  color: #3b3c3d !important;
  font-weight: normal;
}

/* 隐藏表格外围边框线（Element Plus 用伪元素绘制表格外边框） */
.el-table--border {
  border: none !important;
}
.el-table--border::after {
  /* 隐藏右侧边框线 */
  width: 0 !important;
}
.el-table::before {
  /* 隐藏底部边框线 */
  height: 0 !important;
}

/* 隐藏内容区域单元格的边框线（保证内容区无竖线、横线） */
.el-table--border td {
  border: none !important;
}

/* 可选：隐藏表头底部边框线，使表头与内容区域衔接无横线 */
.el-table--border th {
  border-bottom: none !important;
}

// /* 加粗表头每一列右边的边框 */
// .el-table--border th {
//   border-right: 2px solid #dcdfe6 !important; /* 默认是 1px，这里改为 2px，可自行调色 */
// }

// /* 选配：避免表头底部线太细 */
// .el-table--border th.is-leaf {
//   border-bottom: 1px solid transparent !important;
// }
