import request from '@/config/axios'
// 分页搜索
export const getWarnList = async (params) => {
  return await request.get({ url: `/infra/warn-type-dict/page`, params })
}
// 新增
export const createWarn = async (data: any) => {
  return await request.post({ url: `/infra/warn-type-dict/create`, data })
}
// 获取详情回填
export const getWarnDetail = async (params) => {
  return await request.get({ url: `/infra/warn-type-dict/get`, params })
}
// 更新 编辑
export const updateWarn = async (data: any) => {
  return await request.put({ url: `/infra/warn-type-dict/update`, data })
}
// 删除告警方式
export const deleteWarn = async (params) => {
  return await request.delete({ url: `/infra/warn-type-dict/delete`, params })
}
