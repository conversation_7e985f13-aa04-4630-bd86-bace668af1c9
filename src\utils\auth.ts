/**
 * 认证相关工具函数
 * 提供token管理、登录表单缓存、租户信息管理等功能
 */

import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import { TokenType } from '@/api/login/types'
import { decrypt, encrypt } from '@/utils/jsencrypt'

// 缓存实例
const { wsCache } = useCache()

// Token缓存键名常量
const AccessTokenKey = 'ACCESS_TOKEN' as const
const RefreshTokenKey = 'REFRESH_TOKEN' as const

// ========== 类型定义 ==========

/**
 * 登录表单数据类型
 */
export interface LoginFormType {
  /** 租户名称 */
  tenantName: string
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 是否记住我 */
  rememberMe: boolean
}

/**
 * 租户ID类型
 */
export type TenantId = number | string | null

/**
 * 认证状态信息类型
 */
export interface AuthStatusInfo {
  /** 是否已认证 */
  isAuthenticated: boolean
  /** 是否有访问令牌 */
  hasAccessToken: boolean
  /** 是否有刷新令牌 */
  hasRefreshToken: boolean
  /** 是否有登录表单缓存 */
  hasLoginForm: boolean
  /** 是否有租户ID */
  hasTenantId: boolean
  /** 是否有访问租户ID */
  hasVisitTenantId: boolean
  /** 当前租户ID */
  tenantId: TenantId
  /** 当前访问租户ID */
  visitTenantId: TenantId
}

// ========== Token 管理 ==========

/**
 * 获取访问令牌
 * @description 从缓存中获取访问令牌，提供降级处理
 * @returns {string | null} 访问令牌，如果不存在则返回null
 */
export const getAccessToken = (): string | null => {
  // 此处与TokenKey相同，此写法解决初始化时Cookies中不存在TokenKey报错
  const accessToken = wsCache.get(AccessTokenKey)
  return accessToken ? accessToken : wsCache.get('ACCESS_TOKEN')
}

/**
 * 获取刷新令牌
 * @description 从缓存中获取刷新令牌
 * @returns {string | null} 刷新令牌，如果不存在则返回null
 */
export const getRefreshToken = (): string | null => {
  return wsCache.get(RefreshTokenKey)
}

/**
 * 设置令牌信息
 * @description 将访问令牌和刷新令牌存储到缓存中
 * @param {TokenType} token - 包含访问令牌和刷新令牌的对象
 */
export const setToken = (token: TokenType): void => {
  wsCache.set(RefreshTokenKey, token.refreshToken)
  wsCache.set(AccessTokenKey, token.accessToken)
}

/**
 * 删除所有令牌
 * @description 从缓存中删除访问令牌和刷新令牌
 */
export const removeToken = (): void => {
  wsCache.delete(AccessTokenKey)
  wsCache.delete(RefreshTokenKey)
}

/**
 * 格式化令牌为JWT格式
 * @description 为令牌添加Bearer前缀，用于HTTP请求头
 * @param {string} token - 原始令牌
 * @returns {string} 格式化后的令牌字符串
 * @example
 * formatToken('abc123') // 返回: 'Bearer abc123'
 */
export const formatToken = (token: string): string => {
  return 'Bearer ' + token
}

/**
 * 检查访问令牌是否存在
 * @description 判断当前是否有有效的访问令牌
 * @returns {boolean} 如果存在访问令牌返回true，否则返回false
 */
export const hasAccessToken = (): boolean => {
  const token = getAccessToken()
  return !!token && token.trim() !== ''
}

/**
 * 检查是否已登录
 * @description 通过检查访问令牌来判断用户是否已登录
 * @returns {boolean} 如果已登录返回true，否则返回false
 */
export const isAuthenticated = (): boolean => {
  return hasAccessToken()
}
// ========== 登录表单管理 ==========

/**
 * 获取缓存的登录表单数据
 * @description 从缓存中获取登录表单数据，并自动解密密码
 * @returns {LoginFormType | null} 登录表单数据，如果不存在则返回null
 */
export const getLoginForm = (): LoginFormType | null => {
  const loginForm: LoginFormType = wsCache.get(CACHE_KEY.LoginForm)
  if (loginForm) {
    // 解密密码
    const decryptedPassword = decrypt(loginForm.password)
    if (decryptedPassword) {
      loginForm.password = decryptedPassword as string
    }
  }
  return loginForm
}

/**
 * 设置登录表单数据到缓存
 * @description 将登录表单数据加密后存储到缓存中，有效期30天
 * @param {LoginFormType} loginForm - 登录表单数据
 */
export const setLoginForm = (loginForm: LoginFormType): void => {
  // 创建副本以避免修改原始对象
  const formCopy = { ...loginForm }

  // 加密密码
  const encryptedPassword = encrypt(formCopy.password)
  if (encryptedPassword) {
    formCopy.password = encryptedPassword as string
  }

  // 设置30天过期时间
  wsCache.set(CACHE_KEY.LoginForm, formCopy, { exp: 30 * 24 * 60 * 60 })
}

/**
 * 删除缓存的登录表单数据
 * @description 从缓存中删除登录表单数据
 */
export const removeLoginForm = (): void => {
  wsCache.delete(CACHE_KEY.LoginForm)
}

/**
 * 检查是否有缓存的登录表单数据
 * @description 判断是否存在缓存的登录表单数据
 * @returns {boolean} 如果存在返回true，否则返回false
 */
export const hasLoginForm = (): boolean => {
  const loginForm = wsCache.get(CACHE_KEY.LoginForm)
  return !!loginForm
}

// ========== 租户管理 ==========

/**
 * 获取租户ID
 * @deprecated 该接口已废弃，请勿再使用
 * @description 从缓存中获取租户ID
 * @returns {TenantId} 租户ID，如果不存在则返回null
 */
export const getTenantId = (): TenantId => {
  return wsCache.get(CACHE_KEY.TenantId)
}

/**
 * 设置租户ID
 * @description 将租户ID存储到缓存中
 * @param {number} tenantId - 租户ID
 */
export const setTenantId = (tenantId: number): void => {
  wsCache.set(CACHE_KEY.TenantId, tenantId)
}

/**
 * 获取访问租户ID
 * @description 从缓存中获取当前访问的租户ID
 * @returns {TenantId} 访问租户ID，如果不存在则返回null
 */
export const getVisitTenantId = (): TenantId => {
  return wsCache.get(CACHE_KEY.VisitTenantId)
}

/**
 * 设置访问租户ID
 * @description 将访问租户ID存储到缓存中
 * @param {number} visitTenantId - 访问租户ID
 */
export const setVisitTenantId = (visitTenantId: number): void => {
  wsCache.set(CACHE_KEY.VisitTenantId, visitTenantId)
}

/**
 * 删除租户相关信息
 * @description 清除所有租户相关的缓存信息
 */
export const removeTenantInfo = (): void => {
  wsCache.delete(CACHE_KEY.TenantId)
  wsCache.delete(CACHE_KEY.VisitTenantId)
}

/**
 * 检查是否有租户ID
 * @description 判断是否存在租户ID
 * @returns {boolean} 如果存在租户ID返回true，否则返回false
 */
export const hasTenantId = (): boolean => {
  // 直接从缓存获取，避免使用废弃的函数
  const tenantId = wsCache.get(CACHE_KEY.TenantId)
  return tenantId !== null && tenantId !== undefined
}

/**
 * 检查是否有访问租户ID
 * @description 判断是否存在访问租户ID
 * @returns {boolean} 如果存在访问租户ID返回true，否则返回false
 */
export const hasVisitTenantId = (): boolean => {
  const visitTenantId = getVisitTenantId()
  return visitTenantId !== null && visitTenantId !== undefined
}

// ========== 综合工具函数 ==========

/**
 * 清除所有认证相关信息
 * @description 清除所有token、登录表单和租户信息
 */
export const clearAllAuthInfo = (): void => {
  removeToken()
  removeLoginForm()
  removeTenantInfo()
}

/**
 * 获取当前认证状态信息
 * @description 获取当前用户的认证状态概览
 * @returns 认证状态信息对象
 */
export const getAuthStatus = (): AuthStatusInfo => {
  return {
    isAuthenticated: isAuthenticated(),
    hasAccessToken: hasAccessToken(),
    hasRefreshToken: !!getRefreshToken(),
    hasLoginForm: hasLoginForm(),
    hasTenantId: hasTenantId(),
    hasVisitTenantId: hasVisitTenantId(),
    // 直接从缓存获取，避免使用废弃的函数
    tenantId: wsCache.get(CACHE_KEY.TenantId),
    visitTenantId: getVisitTenantId()
  }
}
