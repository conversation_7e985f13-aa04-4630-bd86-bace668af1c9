# 高德地图选择器组件使用指南

## 概述

我已经为您创建了一个完整的高德地图选择器组件 `AMapSelector`，该组件提供了以下功能：

- 📍 点击输入框打开地图选择弹窗
- 🔍 支持地址搜索和自动补全
- 🗺️ 支持地图点击选择位置
- 🎯 支持标记拖拽调整位置
- 🏠 支持逆地理编码获取详细地址
- 🎨 支持自定义弹窗大小和地图高度

## 文件结构

```
src/components/AMapSelector/
├── index.ts                    # 组件导出文件
├── types.ts                    # TypeScript 类型定义
├── src/
│   └── AMapSelector.vue        # 主组件文件
└── README.md                   # 详细文档
```

## 快速开始

### 1. 获取高德地图API密钥

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册并登录账号
3. 创建应用，选择 "Web端(JS API)" 平台
4. 获取API密钥

### 2. 基础使用

```vue
<template>
  <div>
    <AMapSelector
      v-model="selectedLocation"
      placeholder="请选择位置"
      :amap-key="'your-amap-key-here'"
      @change="handleLocationChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const handleLocationChange = (location) => {
  console.log('选择的位置:', location)
}
</script>
```

### 3. 在表单中使用

```vue
<template>
  <el-form :model="formData" label-width="120px">
    <el-form-item label="公司地址：">
      <AMapSelector
        v-model="formData.location"
        placeholder="请选择公司地址"
        :amap-key="amapKey"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'

const formData = reactive({
  location: {
    lng: 0,
    lat: 0,
    address: ''
  }
})
</script>
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | LocationData | - | 绑定值 |
| placeholder | string | '请选择位置' | 输入框占位符 |
| dialogTitle | string | '选择位置' | 弹窗标题 |
| dialogWidth | string | '800px' | 弹窗宽度 |
| mapHeight | string | '400px' | 地图高度 |
| amapKey | string | - | 高德地图API密钥 |
| defaultCenter | [number, number] | [120.088, 30.86] | 默认中心点 |
| defaultZoom | number | 12 | 默认缩放级别 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | LocationData | 值更新事件 |
| change | LocationData | 值改变事件 |

## LocationData 类型

```typescript
interface LocationData {
  lng: number        // 经度
  lat: number        // 纬度
  address: string    // 详细地址
  name?: string      // 位置名称（可选）
}
```

## 示例页面

查看 `src/views/Example/AMapSelectorExample.vue` 文件获取完整的使用示例，包括：

- 基础用法示例
- 自定义配置示例
- 表单中使用示例
- 详细的使用说明

## 注意事项

1. **API密钥配置**：使用前必须配置有效的高德地图API密钥
2. **网络环境**：确保可以访问高德地图服务
3. **内存管理**：组件会在弹窗关闭时自动销毁地图实例
4. **类型安全**：组件提供了完整的TypeScript类型定义

## 自定义样式

组件提供了基础样式，可以通过CSS覆盖来自定义：

```scss
.amap-selector {
  .amap-input {
    // 自定义输入框样式
  }
}

.amap-dialog-content {
  .amap-container {
    // 自定义地图容器样式
  }
}
```

## 已完成的功能

✅ 组件已注册到全局，可以直接使用  
✅ 支持TypeScript类型检查  
✅ 提供完整的文档和示例  
✅ 支持地址搜索和自动补全  
✅ 支持地图交互和位置选择  
✅ 支持自定义配置  

## 下一步

1. 将 `amapKey` 替换为您的实际高德地图API密钥
2. 根据需要调整默认中心点和缩放级别
3. 在您的页面中使用组件
4. 根据项目需求自定义样式

如有任何问题或需要进一步的功能扩展，请随时联系！
