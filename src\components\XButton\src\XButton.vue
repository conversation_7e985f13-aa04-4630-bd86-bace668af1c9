<script lang="ts" setup>
import { PropType } from 'vue'
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'XButton' })

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  preIcon: propTypes.string.def(''),
  postIcon: propTypes.string.def(''),
  title: propTypes.string.def(''),
  type: propTypes.oneOf(['', 'primary', 'success', 'warning', 'danger', 'info']).def(''),
  link: propTypes.bool.def(false),
  circle: propTypes.bool.def(false),
  round: propTypes.bool.def(false),
  plain: propTypes.bool.def(false),
  gradient: propTypes.bool.def(false), // 新增 样式 属性
  width: propTypes.string.def(''), // 新增 width 属性
  onClick: { type: Function as PropType<(...args) => any>, default: null }
})
const buttonStyle = computed(() => ({
  width: props.width ? props.width : '',
}))
const getBindValue = computed(() => {
  const delArr: string[] = ['title', 'preIcon', 'postIcon', 'onClick']
  const attrs = useAttrs()
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})
</script>

<template>
  <el-button v-bind="getBindValue" :style="buttonStyle" :class="{ 'gradient-btn': gradient }" @click="onClick">
    <Icon v-if="preIcon" :icon="preIcon" class="mr-1px" />
    {{ title ? title : '' }}
    <Icon v-if="postIcon" :icon="postIcon" class="mr-1px" />
  </el-button>
</template>
<style lang="scss" scoped>
:deep(.el-button.is-text) {
  padding: 8px 4px;
  margin-left: 0;
}

:deep(.el-button.is-link) {
  padding: 8px 4px;
  margin-left: 0;
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
</style>
