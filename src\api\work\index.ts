import request from '@/config/axios'

export interface BannerVO {
  projectCode: string
  projectName: string
  projectType: number
  oamType: number
  warnType: number
  startDate: string
  endDate: string
  projectAmount: number
}
export interface GetOamProjectPageRes {
  code: number
  data: {
    /*数据 */
    list: {
      /*主键id */
      id: number

      /*项目编号 */
      projectCode: string

      /*项目名称 */
      projectName: string

      /*项目类型(1建设项目，2运维项目) */
      projectType: number

      /*运维类型(1全运维，2抢修运维，3网管运维) */
      oamType: number

      /*告警方式id */
      warnType: number

      /*告警方式 */
      warnTypeName: string

      /*资产数 */
      assetsCount: number

      /*维护开始日期 */
      startDate: Record<string, unknown>

      /*维护结束日期 */
      endDate: Record<string, unknown>

      /*是否在保 */
      safeguardFlag: boolean

      /*项目金额 */
      projectAmount: number

      /*创建者-昵称 */
      creatorNickname: string

      /*创建时间 */
      createTime: Record<string, unknown>

      /*更新者-昵称 */
      updaterNickname: string
    }[]

    /*总量 */
    total: number
  }

  /* */
  msg: string
}
// 获取项目信息数量
export const getProjectCount = async () => {
  return await request.get({ url: `/infra/oam-project/getOamCount` })
}

// 获取告警信息数量
export const getWarningCount = async () => {
  return await request.get({ url: `/infra/warn-assets/getWarnCount` })
}
// 获取设备信息数据
export const getEquipmentBasis = async () => {
  return await request.get({ url: `/infra/device-overview-info/device/statistics` })
}
// 获取排班列表信息数据
// /admin-api/system/omu-schedule/list/date
export const getSchedulingList = async (params) => {
  return await request.get({ url: `/system/omu-schedule/list/date`, params })
}
