import request from '@/config/axios'

export interface BannerVO {
  projectCode: string
  projectName: string
  projectType: number
  oamType: number
  warnType: number
  startDate: string
  endDate: string
  projectAmount: number
}

// 获取搜索的下拉列表
export const getSelect = async () => {
  return await request.get({ url: `/infra/device-type/getDicTree` })
}

// 获取左侧分组列表
export const getGroup = async (params) => {
  return await request.get({ url: `/infra/assets-group/list`, params })
}

// 分页/搜索
export const getList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/page`, data })
}

// 详情
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/device-overview-info/get`, params })
}

// 添加进某一分组
export const addList = async (data) => {
  return await request.put({ url: `/infra/device-overview-info/updateGroupId`, data })
}

// 移除设备的分组
export const removeGroup = async (data) => {
  return await request.put({ url: `/infra/device-overview-info/updateGroupId`, data })
}

// 删除
export const deleteList = async (id: number) => {
  return await request.delete({ url: `/infra/device-overview-info/delete` + id })
}

// 导出
export const exportList = async (params) => {
  return await request.download({ url: `/infra/device-overview-info/export-excel`, params })
}
// 获得日志
export const getLog = async (params) => {
  return await request.get({ url: `/system/operate-log/page`, params })
}
