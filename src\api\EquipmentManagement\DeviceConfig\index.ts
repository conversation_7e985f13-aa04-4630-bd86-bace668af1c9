import request from '@/config/axios'

export interface TenantVO {
  pageNo: number
  pageSize: number
  label: string
  dictType: string
  value: string
  parentId: string
  id: string
  sort: string
  typeCode: string

}


// 设备类型分页
export const getDeviceType = (data: TenantVO) => {
  return request.post({ url: '/infra/device-type/list', data })
}

// 设备类型不分页
export const getTypelist = (data: TenantVO) => {
  return request.post({ url: '/infra/device-type/query/list', data })
}

//字典分页
export const getAccessoryTypePage = (params: TenantVO) => {
  return request.get({ url: '/infra/device-dict/page', params })
}
// 字典新增
export const AccessoryTypeAdd = (data: TenantVO) => {
  return request.post({ url: '/infra/device-dict/create', data })
}
// 字典更新
export const AccessoryTypeUpdate = (data: TenantVO) => {
  return request.put({ url: '/infra/device-dict/update', data })
}
// 字典删除
export const getDicByDictType = (params:TenantVO) => {
  return request.delete({ url: '/infra/device-dict/delete', params })
}
//字典排序
export const DictionarySort = (params: TenantVO) => {
  return request.get({ url: '/infra/device-dict/update/sort', params })
}
//生成随机字典编号
export const RandomCode = (params) => {
  return request.get({ url: '/infra/device-dict/code/generator?dictType='+ params })
}

// 获取设备不分页
export const getPropertyPagination = (data: TenantVO) => {
  return request.post({ url: '/infra/device-overview-info/list', data })
}

// 资产删除
export const deleteProperty = (id: number) => {
  return request.delete({ url: '/infra/device-overview-info/delete?id=' + id })
}

// 获取辅件分页
export const getAccessoriesPage = (data: TenantVO) => {
  return request.post({ url: '/infra/device-auxiliary-info/page', data })
}

// 辅件删除
export const deleteAccessories = (id: number) => {
  return request.delete({ url: '/infra/device-overview-info/delete?id=' + id })
}




// 辅件详情
export const AccDetails = (id: number) => {
  return request.get({ url: 'infra/device-auxiliary-info/get?id=' + id })
}

// 新增租户
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/system/tenant/create', data })
}

// 修改租户
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除租户
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

