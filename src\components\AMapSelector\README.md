# AMapSelector 高德地图选择器组件

一个基于高德地图的位置选择器组件，支持地址搜索、地图点击选择、标记拖拽等功能。

## 功能特性

- 🗺️ 集成高德地图，支持地图显示和交互
- 🔍 支持地址搜索和自动补全
- 📍 支持地图点击选择位置
- 🎯 支持标记拖拽调整位置
- 🏠 支持逆地理编码获取详细地址
- 🎨 支持自定义弹窗大小和地图高度
- 📱 响应式设计，适配不同屏幕尺寸

## 安装依赖

```bash
pnpm add @amap/amap-jsapi-loader
```

## 基础用法

```vue
<template>
  <div>
    <AMapSelector
      v-model="selectedLocation"
      placeholder="请选择位置"
      :amap-key="amapKey"
      @change="handleLocationChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const amapKey = 'your-amap-key-here' // 替换为你的高德地图API密钥

const selectedLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const handleLocationChange = (location) => {
  console.log('选择的位置:', location)
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | LocationData | - | 绑定值，包含 lng、lat、address 字段 |
| placeholder | string | '请选择位置' | 输入框占位符 |
| dialogTitle | string | '选择位置' | 弹窗标题 |
| dialogWidth | string | '800px' | 弹窗宽度 |
| mapHeight | string | '400px' | 地图容器高度 |
| amapKey | string | 'your-amap-key' | 高德地图API密钥 |
| defaultCenter | [number, number] | [120.088, 30.86] | 默认中心点坐标 |
| defaultZoom | number | 12 | 默认缩放级别 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | LocationData | 值更新事件 |
| change | LocationData | 值改变事件 |

## LocationData 类型

```typescript
interface LocationData {
  lng: number        // 经度
  lat: number        // 纬度
  address: string    // 详细地址
  name?: string      // 位置名称（可选）
}
```

## 高级用法

### 自定义配置

```vue
<template>
  <AMapSelector
    v-model="location"
    placeholder="自定义地图选择器"
    dialog-title="请选择您的位置"
    dialog-width="900px"
    map-height="500px"
    :default-center="[120.15, 30.28]"
    :default-zoom="14"
    :amap-key="amapKey"
  />
</template>
```

### 在表单中使用

```vue
<template>
  <el-form :model="formData" label-width="120px">
    <el-form-item label="公司地址：">
      <AMapSelector
        v-model="formData.location"
        placeholder="请选择公司地址"
        :amap-key="amapKey"
      />
    </el-form-item>
  </el-form>
</template>
```

## 获取高德地图API密钥

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册并登录账号
3. 进入控制台，创建应用
4. 选择 "Web端(JS API)" 平台
5. 获取API密钥并替换组件中的 `amapKey` 参数

## 注意事项

1. 使用前需要先获取高德地图的API密钥
2. 确保网络环境可以访问高德地图服务
3. 组件会在弹窗关闭时自动销毁地图实例以释放内存
4. 建议在生产环境中对API密钥进行安全配置

## 样式自定义

组件提供了基础的样式，你可以通过CSS覆盖来自定义样式：

```scss
.amap-selector {
  // 自定义输入框样式
  .amap-input {
    // 你的样式
  }
}

// 自定义弹窗内容样式
.amap-dialog-content {
  .amap-container {
    // 自定义地图容器样式
  }
}
```

## 示例页面

查看 `src/views/Example/AMapSelectorExample.vue` 文件获取完整的使用示例。
