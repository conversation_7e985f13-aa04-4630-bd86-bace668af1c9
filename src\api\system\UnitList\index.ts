import request from '@/config/axios'

export interface TenantVO {
  pageNo: string
  pageSize: string
  status: number
  permissionCode: string
  permissionName: string
  permissionTypeName: string
  deviceId: number
  permissionTypeCode: number
  excludeDeviceIds?: []
  deviceTypeId: number
  projectId: number
  areaId: number
  deptId: number
  typeCode: string
  createTime: Date
}
export interface copeTenantVO {
  pageNo: string
  pageSize: string
  assetsName: string
  assetsTypeId?: []
  permissionDeptId: string
  areaId?: []
}
export interface PermissionData {
  id: number;
  permissionCode: string;
  permissionName: string;
  permissionTypeCode: number;
  permissionTypeName: string;
  permissionId: string;
  deptId: number;
  areaId: number;
  projectId: number;
  deviceTypeId: number;
  deviceId: number;
  excludeDeviceIds: string; // 假设'15695j'是一个有效的字符串，如果需要为数组或其他类型，请相应调整
}

export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  parentId?: string
  project?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 获取指定部门的所有子部门
export const getBranch = (params: TenantPageReqVO) => {
  return request.get({ url: '/system/dept/child/list', params })
}

// 获得设备数据权限总分页
export const getPermissionPage = (data: TenantVO) => {
  return request.post({ url: '/system/device-permission-info/page', data })
}
// 获得设备数据权限不分页
export const getPermissionList = (data: TenantVO) => {
  return request.post({ url: '/system/device-permission-info/list', data })
}

//获得资产总览分页
export const getPropertyPages = (data: copeTenantVO) => {
  return request.post({ url: '/infra/device-overview-info/page', data })
}
//选择资产（查询资产详情）
export const getPropertyDetails = (data: copeTenantVO) => {
  return request.post({ url: '/system/device-permission-info/get/detail', data })
}

// 获得设备类型列表
export const getDeviceType = (params: TenantPageReqVO) => {
  return request.get({ url: '/infra/device-type/getDicTree', params })
}
// 获得运维项目列表，不分页
export const getProjectList = (params: TenantPageReqVO) => {
  return request.get({ url: '/infra/oam-project/list', params })
}

// 设备类型不分页
export const getTypelist = (data: TenantVO) => {
  return request.post({ url: '/infra/device-type/query/list', data })
}

//更新区域权限（用于区域权限）
export const updatePermissions = (data: PermissionData) => {
  return request.post({ url: '/system/device-permission-info/update/area', data })
}
//创建设备数据权限
export const createPermissions = (data: PermissionData) => {
  return request.post({ url: '/system/device-permission-info/create', data })
}

//获取已勾选的资产
export const getPropertyss = (data: TenantVO) => {
  return request.post({ url: 'system/device-permission-info/list', data })
}

// 获取已勾选的资产
export const getProperty = (params) => {
  return request.get({ url: '/system/device-permission-info/exclude?deptId='+ params })
}
// 获取已勾选的资产
export const resetPermission = (params) => {
  return request.get({ url: '/system/device-permission-info/reset/permission?dept='+ params })
}

// 删除设备数据权限
export const deletePermission= (id: number) => {
  return request.delete({ url: '/system/device-permission-info/delete?id=' + id })
}

// 获得字典数据
export const getDicByDictType = (params) => {
  return request.get({ url: '/infra/device-dict/getDicByDictType?dictType='+ params })
}

// 更新设备数据权限(选择资产)
export const updatePermission= (data: TenantVO) => {
  return request.put({ url: '/system/device-permission-info/update', data })
}


// 类型管理更新
export const updateTypePage = (data: TenantVO) => {
  return request.put({ url: '/system/type/update', data })
}




// 查询租户详情
export const getTenant = (id: number) => {
  return request.get({ url: '/admin-api/system/type/page' + id })
}

// 新增租户
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/system/tenant/create', data })
}



// 删除租户
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出租户
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}
