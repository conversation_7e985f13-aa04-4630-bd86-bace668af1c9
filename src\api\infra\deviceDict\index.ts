import request from '@/config/axios'

// 获得设备管理中心字典-某个字典类型的树形结构数据
export const getDic = (params) => {
  return request.get({ url: '/infra/device-dict/getDicByDictType', params })
}
export const getDictList = (dictType: string) => {
  return request.get({ url: '/infra/device-dict/dict/type/list', params: { dictType } })
}
export const getSitePage = (data) => {
  return request.post({ url: '/infra/device-site-info/page', data })
}
export const getDeviceList = (data) => {
  return request.post({ url: '/infra/device-overview-info/list', data })
}
