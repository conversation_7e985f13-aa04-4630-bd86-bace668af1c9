<template>
  <el-form-item :label="field.name" :prop="field.code">
    <!-- 文本输入框 -->
    <el-input
      v-if="field.fieldType === FieldType.TEXT"
      :model-value="modelValue"
      :placeholder="field.remark || `请输入${field.name}`"
      :maxlength="field.length"
      :type="getTextType(field)"
      :rows="getTextRows(field)"
      @update:model-value="val => handleUpdate(typeof val === 'string' ? val.trim() : val)"
    />

    <!-- 数字输入框 -->
    <el-input-number
      v-else-if="field.fieldType === FieldType.NUMBER"
      :model-value="modelValue"
      @update:model-value="handleUpdate"
    />

    <!-- 下拉框/单选框/多选框 -->
    <el-select
      v-else-if="field.fieldType === FieldType.RADIO || field.fieldType === FieldType.CHECKBOX"
      :model-value="modelValue"
      :multiple="field.fieldType === FieldType.CHECKBOX"
      :placeholder="`请选择${field.name}`"
      @update:model-value="handleUpdate"
    >
      <el-option
        v-for="option in getFieldOptions(field)"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 单个时间选择器 -->
    <el-date-picker
      v-else-if="field.fieldType === FieldType.DATE"
      :model-value="modelValue"
      :type="getPickerType(field)"
      :format="getPickerFormat(field)"
      value-format="YYYY/MM/DD HH:mm:ss"
      :placeholder="`请选择${field.name}`"
      class="w-full"
      @update:model-value="handleUpdate"
    />

    <!-- 时间范围选择器 -->
    <el-date-picker
      v-else-if="field.fieldType === FieldType.DATE_RANGE"
      :model-value="modelValue"
      type="datetimerange"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :format="getPickerFormat(field)"
      value-format="YYYY/MM/DD HH:mm:ss"
      :placeholder="`请选择${field.name}`"
      @update:model-value="handleUpdate"
    />

    <!-- 文件上传 -->
    <UploadFile
      v-else-if="field.fieldType === FieldType.ATTACHMENT"
      :model-value="modelValue"
      :file-type="getFileTypes(field)"
      :limit="getFileLimit(field)"
      :file-size="getFileSize(field)"
      class="min-w-80px"
      @update:model-value="handleUpdate"
    />

    <!-- 标签输入 -->
    <el-input
      v-else-if="field.fieldType === FieldType.TAG"
      :model-value="modelValue"
      @update:model-value="handleUpdate"
    />

    <!-- 地区选择 -->
    <el-tree-select
      v-else-if="field.fieldType === FieldType.REGION"
      :data="fieldOptions as any[]"
      :model-value="modelValue"
      :render-after-expand="false"
      @update:model-value="handleUpdate"
    />

    <!-- 默认文本输入 -->
    <el-input
      v-else
      :model-value="modelValue"
      :placeholder="`请输入${field.name}`"
      @update:model-value="handleUpdate"
    />
  </el-form-item>
</template>

<script setup lang="ts">
import { UploadFile } from '@/components/UploadFile'
import { FieldType } from '@/config/constants/enums/field'

interface Option {
  label: string
  value: string | number
}

interface SimpleFieldConfig {
  code: string
  name: string
  fieldType: number
  remark?: string
  length?: number
  fieldConfExtDOList?: Array<{ name: string; value: any; optionsJson?: any[] }>
}

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: Option[]
}

const props = withDefaults(defineProps<Props>(), {
  fieldOptions: () => []
})

const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 获取文本输入框类型
const getTextType = (field: SimpleFieldConfig): string => {
  const textTypeConfig = field.fieldConfExtDOList?.find(item => item.name === 'textType')
  return textTypeConfig?.value === '1' ? 'textarea' : 'text'
}

// 获取文本输入框行数
const getTextRows = (field: SimpleFieldConfig): number => {
  const textTypeConfig = field.fieldConfExtDOList?.find(item => item.name === 'textType')
  return textTypeConfig?.value === '1' ? 2 : 1
}

// 获取时间选择器类型
const getPickerType = (field: SimpleFieldConfig): 'date' | 'datetime' | 'datetimerange' | 'daterange' => {
  const datePrecisionConfig = field.fieldConfExtDOList?.find(item => item.name === 'datePrecision')
  const precision = String(datePrecisionConfig?.value || 'date')

  switch (precision) {
    case 'year':
      return 'date'
    case 'month':
      return 'date'
    case 'date':
      return 'date'
    case 'hour':
    case 'minute':
    case 'second':
      return 'datetime'
    default:
      return 'datetime'
  }
}

// 获取时间选择器格式
const getPickerFormat = (field: SimpleFieldConfig): string => {
  const datePrecisionConfig = field.fieldConfExtDOList?.find(item => item.name === 'datePrecision')
  const precision = String(datePrecisionConfig?.value || 'date')

  switch (precision) {
    case 'year':
      return 'YYYY'
    case 'month':
      return 'YYYY/MM'
    case 'date':
      return 'YYYY/MM/DD'
    case 'hour':
      return 'YYYY/MM/DD HH:00'
    case 'minute':
      return 'YYYY/MM/DD HH:mm'
    case 'second':
      return 'YYYY/MM/DD HH:mm:ss'
    default:
      return 'YYYY/MM/DD HH:mm:ss'
  }
}

// 获取文件类型数组
const getFileTypes = (field: SimpleFieldConfig): string[] => {
  const allowedTypesConfig = field.fieldConfExtDOList?.find(item => item.name === 'allowedTypes')
  if (!allowedTypesConfig?.value) return ['doc', 'xls', 'ppt', 'txt', 'pdf']
  return String(allowedTypesConfig.value).split(',').map(type => type.trim())
}

// 获取文件数量限制
const getFileLimit = (field: SimpleFieldConfig): number => {
  const countLimitConfig = field.fieldConfExtDOList?.find(item => item.name === 'countLimit')
  return countLimitConfig?.value || 5
}

// 获取文件大小限制
const getFileSize = (field: SimpleFieldConfig): number => {
  const sizeLimitConfig = field.fieldConfExtDOList?.find(item => item.name === 'sizeLimit')
  return sizeLimitConfig?.value || 5
}

// 获取字段选项
const getFieldOptions = (field: SimpleFieldConfig): Option[] => {
  const optionsConfig = field.fieldConfExtDOList?.find(item => item.name === 'optionsJson')
  if (optionsConfig?.optionsJson) {
    return optionsConfig.optionsJson
  }
  return props.fieldOptions
}
</script>