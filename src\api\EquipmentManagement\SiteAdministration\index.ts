import request from '@/config/axios'

export interface TenantVO {
  pageNo: number
  pageSize: number
  ids?: number[] // 根据实际情况判断是否为可选字段
  assetsCode?: string
  projectId?: number
  deptId?: number
  assetsTypeId?: number
  assetsSource?: string
  siteId?: number
  areaId?: number
  supplierId?: number
  modelId?: number
  groupId?: number
  warrantyDateStart?: string // 或者使用 Date 类型，取决于前后端交互方式
  warrantyDateEnd?: string   // 同上
  warrantyStatus?: number
  isKey?: number             // 或者使用 boolean 类型，取决于实际值
  labelId?: number
  auxiliaryIds?: number[]
  deviceIds?: number[]
}

export interface Person {
  auxiliaryCode: string;
  auxiliaryName: string;
  areaId:string;
  assetsTypeId:string;
  longitude:string;
  latitude:string;
  picUrl?: string; // 图片 URL 可选
  deviceAssetsAuxiliarySaveReqVOList?: { id: any, deviceId: any }[]; // 可选，根据需求添加
}

export interface TenantPageReqVO {
  pageNo?: string
  pageSize?: string
  value?: string
  ids?: []
}
export interface dataVO {
  pageNo?: string
  pageSize?: string
  siteName?: string
  dictType?: string
  parentId?: string
  areaId?: any
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 获得站点管理分页
export const getSiteAdministrationPage = (data: dataVO) => {
  return request.post({ url: '/infra/device-site-info/page', data })
}
// 获得字典数据
export const getDicByDictType = (params) => {
  return request.get({ url: '/infra/device-dict/getDicByDictType?dictType='+ params })
}

// 站点管理详情
export const AdministrationDetails = (params) => {
  return request.get({ url: '/infra/device-site-info/get?id='+ params })
}

// 删除站点管理
export const deleteAdministration = (id: number) => {
  return request.delete({ url: '/infra/device-site-info/delete?id=' + id })
}

// 获取辅件不分页
export const getAccessoriesList = (data: TenantVO) => {
  return request.post({ url: 'infra/device-auxiliary-info/list', data })
}
// 获取资产不分页
export const getPropertyPagination = (data: TenantVO) => {
  return request.post({ url: '/infra/device-overview-info/list', data })
}
// 创建站点管理
export const CreateSite = (data: TenantVO) => {
  return request.post({ url: 'infra/device-site-info/create', data })
}
// 更新站点管理
export const UpdateSite = (data: TenantVO) => {
  return request.put({ url: 'infra/device-site-info/update', data })
}

// 辅件信息分页
export const getAccessory = (data: TenantVO) => {
  return request.post({ url: 'infra/device-auxiliary-info/page', data })
}

// 获得自定义字段分页
export const getCustomPage = (data: dataVO) => {
  return request.post({ url: 'infra/custom-field-info/page', data })
}
// 创建自定义字段
export const createCustom = (data: dataVO) => {
  return request.post({ url: '/infra/custom-field-info/create', data })
}
// 更新自定义字段
export const updateCustom = (data: dataVO) => {
  return request.put({ url: '/infra/custom-field-info/update', data })
}
// 获得自定义字段不分页
export const getCustomNoPage = (params) => {
  return request.get({ url: 'infra/custom-field-info/list', params })
}

// 创建设备类型
export const createDeviceType = (data: dataVO) => {
  return request.post({ url: '/infra/device-type/create', data })
}

// 导出接口
export const deviceExportexcel = (params:TenantVO) => {
  return request.epupload({ url: '/infra/device-dict/export-excel' ,params })
}
// 导出站点管理
export const exportSiteAdministration = (params) => {
  return request.epupload({ url: 'infra/device-site-info/export-excel', params })
}
// 设备类型导出接口
export const devicetypeExportexcel = (params:TenantVO) => {
  return request.epupload({ url: '/infra/device-type/export-excel' ,params })
}

// 字典导入
export const DictionaryImport = async (data) => {
  return await request.upload({ url: `/infra/device-dict/import/template`,data })
}
// 导入设备类型列表
export const DeviceTypeImport = async (data) => {
  return await request.upload({ url: `/infra/device-type/import/template`,data })
}
// 字典导出
export const DictionaryExport = async (params:TenantPageReqVO) => {
  return await request.epupload({ url: `/infra/device-dict/export-excel`,params })
}
// 字典导入模板
export const ImportTemplate = async (data) => {
  return await request.download({ url: `/infra/device-dict/get/template?dictType=`+ data })
}
// 设备类型导入模板
export const DeviceImportTemplate = async (data) => {
  return await request.download({ url: `/infra/device-type/get/template`, data })
}

// 更新设备类型
export const updateDeviceType = (data: dataVO) => {
  return request.put({ url: '/infra/device-type/update', data })
}

// 获取设备详情
export const getDeviceTypeDetails = (params) => {
  return request.get({ url: '/infra/device-type/get?id='+ params })
}

// 删除设备类型
export const deleteDeviceType = (id: number) => {
  return request.delete({ url: '/infra/device-type/delete?id=' + id })
}

// 删除自定义字段
export const deleteCustom = (id: number) => {
  return request.delete({ url: '/infra/custom-field-info/delete?id=' + id })
}

// 二维码导出记录分页
export const QRExportPage = (params) => {
  return request.get({ url: '/infra/temp-export-log/page', params })
}

// 二维码模板分页
export const codeTemplatePage = (data:TenantVO) => {
  return request.post({ url: '/infra/temp-info/page', data })
}

// 创建二维码模板
export const createQRTemplate = (data) => {
  return request.post({ url: 'infra/temp-info/create', data })
}

// 更新二维码模板
export const updateQRTemplate = (data) => {
  return request.put({ url: 'infra/temp-info/update', data })
}
// 删除二维码模板
export const deleteQRTemplate = (data) => {
  return request.post({ url: 'infra/temp-info/delete', data })
}

// 获取二维码模板详情
export const getQRTemplateEdit = (params) => {
  return request.get({ url: 'infra/temp-info/get?id=' + params })
}


// 获取辅件分页
export const getAccessoriesPage = (data: TenantVO) => {
  return request.post({ url: '/infra/device-auxiliary-info/page', data })
}

// 辅件删除
export const deleteAccessories = (id: number) => {
  return request.delete({ url: '/infra/device-overview-info/delete?id=' + id })
}

// 辅件新增
export const AccessoriesAdd = (data: Person) => {
  return request.post({ url: '/infra/device-auxiliary-info/create', data })
}

// 辅件编辑
export const AccessoriesUpdate = (data: Person) => {
  return request.put({ url: '/infra/device-auxiliary-info/update', data })
}


// 导出租户
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}
