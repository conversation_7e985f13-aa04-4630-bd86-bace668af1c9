<template>
  <el-select
    v-if="type === 'select'"
    :style="{ width: width }"
    v-model="selectValue"
    :multiple="multiple"
    :placeholder="placeholder"
    :clearable="clearable"
    @change="handleChange"
  >
    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>

  <el-cascader
    v-else-if="type === 'cascader'"
    :style="{ width: width }"
    v-model="selectValue"
    :options="options"
    :props="cascaderProps"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="maxCollapseTags"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { getDicByDictType, getPropertyPage } from '@/api/EquipmentManagement/ManagementCenter/index'
const props = defineProps({
  // 控制组件类型：select/cascader
  type: {
    type: String,
    default: 'select',
    validator: (value: string) => ['select', 'cascader'].includes(value)
  },
  width: {
    type: String,
    default: '100%' // 默认宽度
  },
  // 控制单选/多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 接口类型（用于区分不同的数据字典）
  dictType: {
    type: String,
    required: true
  },
  // 默认值
  modelValue: {
    type: [String, Number, Array],
    default: ''
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true
  },
  // cascader 特有属性
  collapseTags: {
    type: Boolean,
    default: true
  },
  // divide  特有属性
  divide: {
    type: Boolean,
    default: true
  },
  maxCollapseTags: {
    type: Number,
    default: 3
  },

  // cascader 配置项
  cascaderProps: {
    type: Object,
    default: () => ({
      value: 'id',
      label: 'name',
      children: 'children',
      multiple: false,
      checkStrictly: false,
      emitPath: false
    })
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectValue = ref()
const options = ref([])

// 监听值的变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectValue.value = newVal
  }
)

// 值变化时触发
const handleChange = (val) => {
  if (props.type === 'cascader' && Array.isArray(val) && props.divide) {
    // 1. 展平数组
    const flatArray = val.flat()
    // 2. 去重
    const uniqueArray = [...new Set(flatArray)]
    // 3. 过滤掉无效值（如 null、undefined）
    const validArray = uniqueArray.filter((item) => item != null)

    emit('update:modelValue', validArray)
    emit('change', validArray)
  } else {
    emit('update:modelValue', val)
    emit('change', val)
  }
}

// 获取字典数据
const loadDictData = async () => {
  try {
    // 这里替换成你的实际 API 调用
    const res = await getDicByDictType(props.dictType)
    options.value = res
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

onMounted(async () => {
  await loadDictData()
  if (options.value) {
    options.value.forEach((item) => {
      if (item.id == props.modelValue) {
        selectValue.value = item.id
      }
    })
  }
})
</script>
