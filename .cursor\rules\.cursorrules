// Vue 3 Composition API .cursorrules

// Vue 3 Composition API best practices

const vue3CompositionApiBestPractices = [
  "Use setup() function for component logic",
  "Utilize ref and reactive for reactive state",
  "Implement computed properties with computed()",
  "Use watch and watchEffect for side effects",
  "Implement lifecycle hooks with onMounted, onUpdated, etc.",
  "Utilize provide/inject for dependency injection",
];

// Folder structure

const folderStructure = `
src/
  components/
  composables/
  views/
  router/
  store/
  assets/
  App.vue
  main.js
`;

// Additional instructions

const additionalInstructions = `
1. Use TypeScript for type safety
2. Implement proper props and emits definitions
3. Utilize Vue 3's Teleport component when needed
4. Use Suspense for async components
5. Implement proper error handling
6. Follow Vue 3 style guide and naming conventions
7. Use Vite for fast development and building
`;
