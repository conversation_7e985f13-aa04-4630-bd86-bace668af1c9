import request from '@/config/axios'
// 资产总览不分页 设备查询不分页 (第一个接口)
export const postListNoPage = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/list`, data })
}
// 左侧树状列表
export const getTreeList = async () => {
  return await request.get({ url: `/infra/device-type/get` })
}
// 获得异常告警设备列分页 (第二个接口)
export const getWarnList = async (params) => {
  return await request.get({ url: `/infra/warn-abnormal-assets/page`, params })
}
//导出异常告警设备列 Excel
export const exportWarnList = async (params) => {
  return await request.download({ url: `/infra/warn-abnormal-assets/export-excel`, params })
}
// 详情左侧 获得资产总览(设备编号)
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/device-overview-info/getByCode`, params })
}
// 详情右侧上面
export const getDetailRightList = async (params) => {
  return await request.get({ url: `/infra/warn-assets/page`, params })
}
// 主页右上角 告警规则
export const getWarnRule = async () => {
  return await request.get({ url: `/infra/warn-abnormal-config/getOpen` })
}
// 移出
export const removeWarn = async (params) => {
  return await request.delete({ url: `/infra/warn-abnormal-assets/delete`, params })
}
