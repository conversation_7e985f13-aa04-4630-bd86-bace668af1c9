import request from '@/config/axios'
// 分页查询
export const getSourceList = async (params) => {
  return await request.get({ url: '/infra/warn-source/page', params })
}
// 暂停
export const paused = async (params) => {
  return await request.put({ url: '/infra/warn-source/pause', params })
}
// 恢复
export const restore = async (params) => {
  return await request.put({ url: '/infra/warn-source/start', params })
}
// 删除
export const remove = async (params) => {
  return await request.delete({ url: '/infra/warn-source/delete', params })
}
// 详情底部
export const getDetailBottom = async (params) => {
  return await request.get({ url: '/infra/warn-source-param/list', params })
}
