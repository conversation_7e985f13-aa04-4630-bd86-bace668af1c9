<template>
  <div class="flex gap-2 items-center">
    <el-input v-model="latitude" placeholder="经度" style="width: 45%" @input="updateValue" />
    <el-input v-model="longitude" placeholder="纬度" style="width: 45%" @input="updateValue" />
    <el-button :icon="Position" @click="dialogVisible = true" />
    <Dialog v-model="dialogVisible" width="1000px" title="经纬度选择">
      <MapPoints ref="mapPointRef" showFind v-model="choosedLngAndLat" />
      <div style="display: flex; align-items: center; justify-content: center; margin-top: 20px">
        <XButton title="确认" width="160px" type="primary" gradient @click="handleConfirm" />
        <XButton title="重置" width="160px" @click="handleReset" />
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { center } from '@/utils/mapmostUtils'
import { Position } from '@element-plus/icons-vue'

const props = defineProps<{
  modelValue: { latitude: string | number; longitude: string | number }
}>()

const emit = defineEmits(['update:modelValue'])

const latitude = ref(props.modelValue?.latitude || '')
const longitude = ref(props.modelValue?.longitude || '')

const updateValue = () => {
  emit('update:modelValue', {
    latitude: latitude.value,
    longitude: longitude.value
  })
}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      latitude.value = val?.latitude
      longitude.value = val?.longitude
    }
  },
  { deep: true }
)

// Dialog 和 地图选点逻辑
const dialogVisible = ref(false)
const choosedLngAndLat = ref({ lng: center[0], lat: center[1] })

const handleConfirm = () => {
  latitude.value = choosedLngAndLat.value.lat
  longitude.value = choosedLngAndLat.value.lng
  updateValue()
  dialogVisible.value = false
}

const handleReset = () => {
  choosedLngAndLat.value = { lng: center[0], lat: center[1] }
}
</script>
