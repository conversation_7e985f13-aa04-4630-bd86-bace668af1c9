{"version": "2.0.0", "tasks": [{"label": "Git: Add All", "type": "shell", "command": "git add .", "problemMatcher": []}, {"label": "Git: Commit", "type": "shell", "command": "git commit -m '${input:commitMessage}'", "problemMatcher": [], "dependsOn": ["Git: Add All"]}, {"label": "Git: <PERSON>ush All Remotes", "type": "shell", "command": "git push origin HEAD && git push github HEAD && git push wx HEAD", "problemMatcher": [], "dependsOn": ["Git: Commit"]}, {"label": "Git: Auto Commit & Push All", "dependsOn": ["Git: Add All", "Git: Commit", "Git: <PERSON>ush All Remotes"], "dependsOrder": "sequence", "problemMatcher": []}], "inputs": [{"id": "commitMessage", "type": "promptString", "description": "请输入本次提交的 commit message"}]}