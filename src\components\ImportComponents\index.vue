<template>
  <Dialog v-model="dialogVisible" :title="title" width="500">
<!--    :action="importUrl + '?updateSupport=' + updateSupport"-->
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      action="#"
      :auto-upload="false"
      :disabled="formLoading"
      :headers="uploadHeaders"
      :limit="1"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      accept=".xlsx, .xls"
      drag
    >
      <Icon icon="ep:upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <div class="el-upload__tip">
            <el-checkbox v-model="updateSupport" />
            是否更新已经存在的数据
          </div>
          <span>仅允许导入 xls、xlsx 格式文件。</span>
          <el-link
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            type="primary"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div style="text-align: center">
        <el-button :disabled="formLoading" class="gradient-btn"  @click="submitForm">确定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as UserApi from '@/api/system/user'
import download from '@/utils/download'

const props = defineProps({
  title: {
    type: String,
    default: '用户导入',
  },
  path: {
    type: String,
    default: '',
    required: true
  },
  downloadPath: {
    type: String,
    default: '/system/user/importTemplate',
    required: true
  } ,

  fileName: {
    type: String,
    default: '导入模板',
  },
  projectId: {
    type: Number,
    default: null, // 或使用 null
  },
  areaId: {
    type: Number,
    default: null, // 或使用 null
  },

})

defineOptions({ name: 'SystemUserImportForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const uploadRef = ref()
const importUrl = import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL + props.path
const uploadHeaders = ref() // 上传 Header 头
const fileList = ref([]) // 文件列表
const updateSupport = ref(0) // 是否更新已经存在的用户数据

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  updateSupport.value = 0
  fileList.value = []
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const emits = defineEmits(['success'])
/** 提交表单 */
const submitForm = async () => {
  if (fileList.value.length == 0) {
    message.error('请上传文件')
    return
  }
  const formData = new FormData()
  if (fileList.value.length > 0) {
    formData.append('file', fileList.value[0].raw)
  }
  if (props.projectId){
    formData.append('projectId', props.projectId.toString())
  }
  if (props.areaId){
    formData.append('areaId', props.areaId.toString())
  }
  formLoading.value = true
  try {
    const res = await UserApi.DownloadInterface(props.path,formData)
    if (res.code !== 0) {
      // 拼接失败提示语
      let text = '导入失败: ' + res.msg
      message.alert(text)
      formLoading.value = false
      return
    }
    // 拼接成功提示语
    const data = res.data
    console.log('走了成功路线', data)
    let text = ''
    text += '上传成功数量：' + data.successCodes.length + '个\n'
    // text += '上传失败数量：' + data.failCodes.length + '个\n'
    text += '上传失败数量：' + Object.keys(data.failCodes).length + '个'
    // 增加对 failCodes 的处理
    if (data.failCodes && Object.keys(data.failCodes).length > 0) {
      text += '失败信息\n'
      for (const code in data.failCodes) {
        text += `${code}: ${data.failCodes[code]}; \n`
      }
    }
    await message.alert(text) // 等待 alert 完成
    dialogVisible.value = false // 关闭弹出框
    emits('success')
  } catch (error) {
    message.error('导入失败')
  } finally {
    formLoading.value = false
  }
}

/** 文件上传成功 */

// const submitFormSuccess = (response: any) => {
//   if (response.code !== 0) {
//     message.error(response.msg)
//     formLoading.value = false
//     return
//   }
//   // 拼接提示语
//   const data = response.data
//   let text = '上传成功数量：' + data.createUsernames.length + ';'
//   for (let username of data.createUsernames) {
//     text += '< ' + username + ' >'
//   }
//   text += '更新成功数量：' + data.updateUsernames.length + ';'
//   for (const username of data.updateUsernames) {
//     text += '< ' + username + ' >'
//   }
//   text += '更新失败数量：' + Object.keys(data.failureUsernames).length + ';'
//   for (const username in data.failureUsernames) {
//     text += '< ' + username + ': ' + data.failureUsernames[username] + ' >'
//   }
//   message.alert(text)
//   formLoading.value = false
//   dialogVisible.value = false
//   // 发送操作成功的事件
//   emits('success')
// }

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}

/** 重置表单 */
const resetForm = async (): Promise<void> => {
  // 重置上传状态和文件
  formLoading.value = false
  await nextTick()
  uploadRef.value?.clearFiles()
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个文件！')
}

/** 下载模板操作 */
const importTemplate = async () => {
  const res = await UserApi.DownloadTemplate(props.downloadPath)
  download.excel(res, props.fileName + '.xlsx')
}
</script>
<style lang="scss" scoped>
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
</style>
