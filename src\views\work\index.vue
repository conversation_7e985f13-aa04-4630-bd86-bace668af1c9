<template>
  <div class="my-component">
    <!-- 高德地图组件演示 -->
    <div class="amap-demo-section">
      <h2>高德地图选择器组件演示</h2>

      <div class="demo-content">
        <div class="form-section">
          <h3>基础使用</h3>
          <div class="form-item">
            <label>选择位置：</label>
            <AMapSelector
              v-model="selectedLocation"
              placeholder="点击选择位置"
              @change="handleLocationChange"
            />
          </div>

          <div v-if="selectedLocation.address" class="result-display">
            <h4>选中的位置信息：</h4>
            <p><strong>地址：</strong>{{ selectedLocation.address }}</p>
            <p><strong>经度：</strong>{{ selectedLocation.lng }}</p>
            <p><strong>纬度：</strong>{{ selectedLocation.lat }}</p>
            <p v-if="selectedLocation.name"><strong>名称：</strong>{{ selectedLocation.name }}</p>
          </div>
        </div>

        <div class="form-section">
          <h3>自定义配置</h3>
          <div class="form-item">
            <label>自定义地图：</label>
            <AMapSelector
              v-model="customLocation"
              placeholder="自定义地图选择器"
              dialog-title="请选择您的位置"
              dialog-width="900px"
              map-height="500px"
              :default-center="[120.15, 30.28]"
              :default-zoom="14"
            />
          </div>
        </div>

        <div class="form-section">
          <h3>表单中使用</h3>
          <el-form :model="formData" label-width="120px">
            <el-form-item label="公司名称：">
              <el-input v-model="formData.companyName" placeholder="请输入公司名称" />
            </el-form-item>

            <el-form-item label="公司地址：">
              <AMapSelector
                v-model="formData.location"
                placeholder="请选择公司地址"
              />
            </el-form-item>

            <el-form-item label="联系电话：">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 头部标题展示 -->
    <header style="margin-top: 40px;">
      <!-- <div class="header-box">
        <div class="header-box-left">
          <img src="@/assets/imgs/icons/workPorjectAllBig.png" alt="" />
        </div>
        <div class="header-box-right">
          <div>
            <p>项目总数</p>
            <p @click="goProject(1)">{{ ProjectCount.allCount }}</p>
          </div>
          <div>
            <p>在保项目</p>
            <p @click="goProject(2)">{{ ProjectCount.safeguardCount }}</p>
          </div>
          <div>
            <p>临近过保</p>
            <p @click="goProject(3)">{{ ProjectCount.nearNoSafeguardCount }}</p>
          </div>
          <div>
            <p>已过保</p>
            <p @click="goProject(4)">{{ ProjectCount.noSafeguardCount }}</p>
          </div>
        </div>
      </div> -->

      <!-- <div class="header-box">
        <div class="header-box-left">
          <img src="@/assets/imgs/icons/wrokEquipmentAllBig.png" alt="" />
        </div>
        <div class="header-box-right">
          <div>
            <p>设备总数</p>
            <p @click="goEquipment(-1)">{{ equipmentBasis.deviceTotal }}</p>
          </div>
          <div>
            <p>完好率</p>
            <p
              >{{
                (equipmentBasis.rate == null ? 0 : parseFloat(equipmentBasis.rate) || 0).toFixed(2)
              }}%</p
            >
          </div>
          <div>
            <p>正常</p>
            <p @click="goEquipment(0)">{{ equipmentBasis.normal }}</p>
          </div>
          <div>
            <p>报备</p>
            <p @click="goEquipment(2)">{{ equipmentBasis.report }}</p>
          </div>
          <div>
            <p>故障</p>
            <p @click="goEquipment(1)">{{ equipmentBasis.malfunction }}</p>
          </div>
        </div>
      </div> -->
<!--
      <div class="header-box">
        <div class="header-box-left">
          <img src="@/assets/imgs/icons/workWarningAllBig.png" alt="" />
        </div>
        <div class="header-box-right">
          <div>
            <p>告警总数</p>
            <p @click="goAlarmList(-2)">{{ WarningCount.allCount }}</p>
          </div>
          <div>
            <p>当日新增</p>
            <p @click="goAlarmList(-1)">{{ WarningCount.todayCount }}</p>
          </div>
          <div>
            <p>未处理</p>
            <p @click="goAlarmList(0)">{{ WarningCount.noDealCount }}</p>
          </div>
          <div>
            <p>处理中</p>
            <p @click="goAlarmList(1)">{{ WarningCount.dealingCount }}</p>
          </div>
        </div>
      </div> -->

      <!-- <div class="header-box">
        <div class="header-box-left">
          <img src="@/assets/imgs/icons/workTicketsAllBig.png" alt="" />
        </div>
        <div class="header-box-right">
          <div>
            <p>工单总数</p>
            <p @click="goTicketsAll(1)">160</p>
          </div>
          <div>
            <p>待处理</p>
            <p @click="goTicketsAll(1)">60</p>
          </div>
          <div>
            <p>处理中</p>
            <p @click="goTicketsAll(1)">50</p>
          </div>
          <div>
            <p>已完结</p>
            <p @click="goTicketsAll(1)">160</p>
          </div>
        </div>
      </div> -->
    </header>
    <!-- 主体Echarts -->
    <!-- <main>
      <el-row :gutter="10" class="w1/1 h1/1">
        <el-col :span="6" class="left-box">
          <div class="left-box-top">
            <LeftTop />
          </div>
          <div class="left-box-bottom">
            <div class="bottom-left">
              <LeftBottom :project-count="ProjectCount" />
            </div>
            <div class="bottom-right">
              <LeftBottomTabs :project-count="ProjectCount" />
            </div>
          </div>
        </el-col>
        <el-col :span="9" class="main-box">
          <div class="main-box-top">
            <MainTop :warning-count="WarningCount" :equipment-basis="equipmentBasis" />
          </div>
          <div class="main-box-bottom">
            <MainBottom :equipment-basis="equipmentBasis" />
          </div>
        </el-col>
        <el-col :span="9" class="right-box">
          <div class="right-box-top">
            <RightTop />
          </div>
          <div class="right-box-bottom">
            <RightBottom />
          </div>
        </el-col>
      </el-row>
    </main> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const customLocation = ref({
  lng: 0,
  lat: 0,
  address: ''
})

const formData = reactive({
  companyName: '',
  location: {
    lng: 0,
    lat: 0,
    address: ''
  },
  phone: ''
})

// 位置改变处理
const handleLocationChange = (location: any) => {
  console.log('位置已选择:', location)
  ElMessage.success(`已选择位置: ${location.address}`)
}

// 提交表单
const submitForm = () => {
  if (!formData.companyName) {
    ElMessage.warning('请输入公司名称')
    return
  }

  if (!formData.location.address) {
    ElMessage.warning('请选择公司地址')
    return
  }

  if (!formData.phone) {
    ElMessage.warning('请输入联系电话')
    return
  }

  console.log('表单数据:', formData)
  ElMessage.success('表单提交成功！')
}

// 重置表单
const resetForm = () => {
  formData.companyName = ''
  formData.location = {
    lng: 0,
    lat: 0,
    address: ''
  }
  formData.phone = ''
  ElMessage.info('表单已重置')
}
</script>

<style lang="scss" scoped>
.my-component {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.amap-demo-section {
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  h2 {
    color: #303133;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    border-bottom: 2px solid #409eff;
    padding-bottom: 15px;
  }

  .demo-content {
    .form-section {
      margin-bottom: 40px;
      padding: 25px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      background: #fafafa;

      h3 {
        color: #409eff;
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: 600;
      }

      .form-item {
        margin-bottom: 20px;
        display: flex;
        align-items: center;

        label {
          display: inline-block;
          width: 120px;
          font-weight: 500;
          color: #606266;
          font-size: 14px;
        }

        :deep(.amap-selector) {
          flex: 1;
          max-width: 400px;
        }
      }

      .result-display {
        margin-top: 20px;
        padding: 20px;
        background: #f0f9ff;
        border-radius: 6px;
        border-left: 4px solid #409eff;

        h4 {
          color: #303133;
          margin-bottom: 12px;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.6;

          strong {
            color: #303133;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 22px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-input {
      max-width: 300px;
    }
  }

  .el-button {
    margin-right: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .my-component {
    padding: 10px;
  }

  .amap-demo-section {
    padding: 20px;

    .demo-content .form-section .form-item {
      flex-direction: column;
      align-items: flex-start;

      label {
        width: auto;
        margin-bottom: 8px;
      }

      :deep(.amap-selector) {
        width: 100%;
        max-width: none;
      }
    }
  }
}
</style>
