import request from '@/config/axios'
export interface TenantPageReqVO extends PageParam {
  dictType?: string
}
// 所属项目
export const getBelongsProject = async () => {
  return await request.get({ url: `/infra/oam-project/list` })
}
// 所属单位
export const getBelongsUnit = async () => {
  return await request.get({ url: `/system/dept/list` })
}
// 类型
export const getType = async () => {
  return await request.get({ url: `/infra/device-type/getDicTree` })
}
// 站点 pageSize传-1获取全部
export const getSite = async (data) => {
  return await request.post({ url: `/infra/device-site-info/page`, data })
}
// 供应商 （dictType =supplierDirectory）
// 规格型号 （dictType = modelConfig）
// 设备标签 （dictType = deviceLabel）
// 区域 （dictType = oamArea）

export const getSuppliers = async (params) => {
  return await request.get({ url: `/infra/device-dict/getDicByDictType`, params })
}
// 保障分组
export const getPacket = async () => {
  return await request.get({ url: `/infra/assets-group/list` })
}
// 告警方式
export const getWarning = async () => {
  return await request.get({ url: `/infra/warn-type-dict/list` })
}
// 根据code查询单位类型 code:operation 运营单位 code:maintenance 维护单位
/*
{
	"code": 0,
	"data": 19,//单位类型id
	"msg": ""
}
*/
export const getUnitType = async (params) => {
  return await request.get({ url: `/system/type/type/code`, params })
}
// 运营单位 （typeId = 运维单位id）
// 维保单位 （typeId = 维护单位id）
export const getUnit = async (params) => {
  return await request.get({ url: `/system/dept/simple-list/opr`, params })
}

//单位只查最高一级
export const getUnitMax = async (params) => {
  return await request.get({ url: `/system/dept/simple-list/opr/top`, params })
}

// 获得设备类型列表
export const getDeviceType = (params: TenantPageReqVO) => {
  return request.get({ url: '/infra/device-type/getDicTree', params })
}
