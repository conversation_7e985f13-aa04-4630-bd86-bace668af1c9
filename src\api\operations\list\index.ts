import request from '@/config/axios'

export interface BannerVO {
  projectCode: string
  projectName: string
  projectType: number
  oamType: number
  warnType: number
  startDate: string
  endDate: string
  projectAmount: number
}
export interface GetOamProjectPageRes {
  code: number
  data: {
    /*数据 */
    list: {
      /*主键id */
      id: number

      /*项目编号 */
      projectCode: string

      /*项目名称 */
      projectName: string

      /*项目类型(1建设项目，2运维项目) */
      projectType: number

      /*运维类型(1全运维，2抢修运维，3网管运维) */
      oamType: number

      /*告警方式id */
      warnType: number

      /*告警方式 */
      warnTypeName: string

      /*资产数 */
      assetsCount: number

      /*维护开始日期 */
      startDate: Record<string, unknown>

      /*维护结束日期 */
      endDate: Record<string, unknown>

      /*是否在保 */
      safeguardFlag: boolean

      /*项目金额 */
      projectAmount: number

      /*创建者-昵称 */
      creatorNickname: string

      /*创建时间 */
      createTime: Record<string, unknown>

      /*更新者-昵称 */
      updaterNickname: string
    }[]

    /*总量 */
    total: number
  }

  /* */
  msg: string
}
export const getYear = async (params) => {
  return await request.get({ url: `/infra/oam-project/getOamByYear`, params })
}

// 分页/搜索
export const getList = async (params) => {
  return await request.get({ url: `/infra/oam-project/page`, params })
}
// 详情
export const getDetail = async (params) => {
  return await request.get({ url: `/infra/oam-project/get`, params })
}

// 添加
export const addList = async (data: BannerVO) => {
  return await request.post({ url: `/infra/oam-project/create`, data })
}
// 编辑/修改
export const updateItem = async (data: BannerVO) => {
  return await request.put({ url: `/infra/oam-project/update`, data })
}

// 删除
export const deleteList = async (id: number) => {
  return await request.delete({ url: `/infra/oam-project/delete?id=` + id })
}

// 详情右侧表格数据获取
// /infra/device-overview-info/page
// export const getDetailList = async (data) => {
//   return await request.post({ url: `/infra/device-overview-info/list`, data })
// }
export const getDetailList = async (data) => {
  return await request.post({ url: `/infra/device-overview-info/page`, data })
}
// 导入
export const importList = async (data) => {
  return await request.upload({ url: `/infra/oam-project/import`, data })
}
// 导出
export const exportList = async (params) => {
  return await request.download({ url: `/infra/oam-project/export-excel`, params })
}

// 导出模版
export const exportListStencil = async () => {
  return await request.download({ url: `/infra/oam-project/get-import-template` })
}
// 导入
export interface importInterface {
  file: string
}
export const importEquipmentList = async (data: importInterface) => {
  return await request.upload({ url: `/infra/oam-project/import`, data })
}

export const ComponentimportSingle = async (data) => {
  return await request.upload({ url: `/infra/device-overview-info/import/template`, data })
}

// 设备导入(单行的设备导入传id)
export const importEquipmentSingle = async (data) => {
  return await request.upload({ url: `/infra/device-overview-info/import/template`, data })
}
// 设备导出模版 (单行设备导入的)
export const exportSingleStencil = async () => {
  return await request.download({ url: `/infra/device-overview-info/get/template` })
}
// 设备导出模版 (无项目的)
export const exportSingleStencilNoProject = async () => {
  return await request.download({ url: `/infra/device-overview-info/get/template-no-project` })
}
// 获得告警方式配置列表不分页(告警方式下拉框)
export const getSelect = async () => {
  return await request.get({ url: `/infra/warn-type-dict/list` })
}
