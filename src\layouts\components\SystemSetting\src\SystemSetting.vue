<script setup>
import settingIcon from './settingIcon.png'
import { usePermissionStore } from '@/store/modules/permission'

const permissionStore = usePermissionStore()

const systemItem = computed(() => {
  return permissionStore.getRouters?.find((v, i) => v.meta.title === '系统管理') || undefined
})

const router = useRouter() // 路由
const handleMenu = () => {
  const item = systemItem.value
  let path = item.path
  if (item?.children && item?.children.length > 0 && item.path !== item.children[0].name) {
    path += getDeepestPath(item?.children || [])
  }
  router.push({ path })
  const children = unref(item.children) || []
  permissionStore.setChildRoutes(children)
}

function getDeepestPath(menu) {
  let fullPath = ''
  function traverse(node) {
    if (!node) return
    fullPath += '/' + node.path
    if (node.children && node.children.length > 0) {
      traverse(node.children[0])
    }
  }
  if (Array.isArray(menu) && menu.length > 0) {
    traverse(menu[0])
  }
  return fullPath.replace(/\/+/g, '/') // 清除多余的斜杠
}
</script>

<template>
  <div v-if="!!systemItem">
    <ElBadge>
      <img @click="handleMenu" class="cursor-pointer size-[30px] mt-2" :src="settingIcon" />
    </ElBadge>
  </div>
</template>
