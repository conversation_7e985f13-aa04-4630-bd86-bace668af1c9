/**
 * 类型判断工具函数集合
 * 提供各种数据类型的判断方法，支持 TypeScript 类型守卫
 *
 * @description 基于 vben-admin 的类型判断工具，增强了类型安全性和注释
 */

// 获取对象原型的 toString 方法，用于精确的类型判断
const toString = Object.prototype.toString

// ========== 基础类型判断 ==========

/**
 * 基础类型判断函数
 * @param val - 待判断的值
 * @param type - 期望的类型名称
 * @returns 是否为指定类型
 * @example
 * is('hello', 'String') // 返回: true
 * is(123, 'Number') // 返回: true
 */
export const is = (val: unknown, type: string): boolean => {
  return toString.call(val) === `[object ${type}]`
}

/**
 * 判断值是否已定义（不为 undefined）
 * @param val - 待判断的值
 * @returns 类型守卫，如果已定义返回 true
 * @example
 * isDef('hello') // 返回: true
 * isDef(undefined) // 返回: false
 */
export const isDef = <T = unknown>(val?: T): val is T => {
  return typeof val !== 'undefined'
}

/**
 * 判断值是否未定义（为 undefined）
 * @param val - 待判断的值
 * @returns 类型守卫，如果未定义返回 true
 * @example
 * isUnDef(undefined) // 返回: true
 * isUnDef('hello') // 返回: false
 */
export const isUnDef = <T = unknown>(val?: T): val is undefined => {
  return typeof val === 'undefined'
}

/**
 * 判断值是否为对象类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是对象返回 true
 * @example
 * isObject({}) // 返回: true
 * isObject(null) // 返回: false
 * isObject([]) // 返回: false
 */
export const isObject = (val: any): val is Record<string, any> => {
  return val !== null && is(val, 'Object')
}

/**
 * 判断值是否为空
 * @param val - 待判断的值
 * @returns 如果为空返回 true
 * @description 支持判断 null、undefined、空字符串、空数组、空对象、空 Map、空 Set
 * @example
 * isEmpty(null) // 返回: true
 * isEmpty('') // 返回: true
 * isEmpty([]) // 返回: true
 * isEmpty({}) // 返回: true
 */
export const isEmpty = (val: any): boolean => {
  if (val === null || val === undefined) {
    return true
  }

  if (isArray(val) || isString(val)) {
    return val.length === 0
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0
  }

  return false
}

// ========== 具体类型判断 ==========

/**
 * 判断值是否为 Date 类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 Date 返回 true
 */
export const isDate = (val: unknown): val is Date => {
  return is(val, 'Date')
}

/**
 * 判断值是否为 null
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 null 返回 true
 */
export const isNull = (val: unknown): val is null => {
  return val === null
}

/**
 * 判断值是否同时为 null 和 undefined（实际上不可能同时为两者）
 * @param val - 待判断的值
 * @returns 类型守卫，永远返回 false
 * @deprecated 此函数逻辑有误，建议使用 isNullOrUnDef
 */
export const isNullAndUnDef = (val: unknown): val is never => {
  return isUnDef(val) && isNull(val) // 这个条件永远不会为 true
}

/**
 * 判断值是否为 null 或 undefined
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 null 或 undefined 返回 true
 */
export const isNullOrUnDef = (val: unknown): val is null | undefined => {
  return isUnDef(val) || isNull(val)
}

/**
 * 判断值是否为数字类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是数字返回 true
 * @example
 * isNumber(123) // 返回: true
 * isNumber('123') // 返回: false
 */
export const isNumber = (val: unknown): val is number => {
  return is(val, 'Number')
}

/**
 * 判断值是否为 Promise 类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 Promise 返回 true
 */
export const isPromise = <T = any>(val: unknown): val is Promise<T> => {
  return is(val, 'Promise') && isObject(val) && isFunction(val.then) && isFunction(val.catch)
}

/**
 * 判断值是否为字符串类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是字符串返回 true
 */
export const isString = (val: unknown): val is string => {
  return is(val, 'String')
}

/**
 * 判断值是否为函数类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是函数返回 true
 */
export const isFunction = (val: unknown): val is Function => {
  return typeof val === 'function'
}

/**
 * 判断值是否为布尔类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是布尔值返回 true
 */
export const isBoolean = (val: unknown): val is boolean => {
  return is(val, 'Boolean')
}

/**
 * 判断值是否为正则表达式类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是正则表达式返回 true
 */
export const isRegExp = (val: unknown): val is RegExp => {
  return is(val, 'RegExp')
}

/**
 * 判断值是否为数组类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是数组返回 true
 */
export const isArray = (val: any): val is Array<any> => {
  return Array.isArray(val)
}

/**
 * 判断值是否为 Window 对象
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 Window 对象返回 true
 */
export const isWindow = (val: any): val is Window => {
  return typeof window !== 'undefined' && is(val, 'Window')
}

/**
 * 判断值是否为 DOM 元素
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 DOM 元素返回 true
 */
export const isElement = (val: unknown): val is Element => {
  return isObject(val) && !!val.tagName
}

/**
 * 判断值是否为 Map 类型
 * @param val - 待判断的值
 * @returns 类型守卫，如果是 Map 返回 true
 */
export const isMap = (val: unknown): val is Map<any, any> => {
  return is(val, 'Map')
}

// ========== 环境判断 ==========

/**
 * 判断当前是否为服务端环境
 */
export const isServer: boolean = typeof window === 'undefined'

/**
 * 判断当前是否为客户端环境
 */
export const isClient: boolean = !isServer

/**
 * 判断是否为深色主题
 * @returns 如果是深色主题返回 true
 */
export const isDark = (): boolean => {
  if (isServer) return false
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

// ========== 格式验证 ==========

/**
 * 判断字符串是否为有效的 URL
 * @param path - 待判断的字符串
 * @returns 如果是有效 URL 返回 true
 * @description 修复了 hash 路由无法跳转的问题
 * @example
 * isUrl('https://example.com') // 返回: true
 * isUrl('invalid-url') // 返回: false
 */
export const isUrl = (path: string): boolean => {
  if (!path || !isString(path)) return false

  const reg = /(((^https?:(?:\/\/)?)(?:[-:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%#\/.\w-_]*)?\??(?:[-\+=&%@.\w_]*)#?(?:[\w]*))?)$/
  return reg.test(path)
}

/**
 * 判断字符串是否为图片链接
 * @param path - 待判断的字符串
 * @returns 如果是图片链接返回 true
 * @example
 * isImgPath('https://example.com/image.jpg') // 返回: true
 * isImgPath('data:image/png;base64,...') // 返回: true
 */
export const isImgPath = (path: string): boolean => {
  if (!path || !isString(path)) return false
  return /(https?:\/\/|data:image\/).*?\.(png|jpg|jpeg|gif|svg|webp|ico)/gi.test(path)
}

// ========== 空值判断 ==========

/**
 * 判断值是否为空值（空字符串、null、undefined）
 * @param val - 待判断的值
 * @returns 如果是空值返回 true
 * @example
 * isEmptyVal('') // 返回: true
 * isEmptyVal(null) // 返回: true
 * isEmptyVal(undefined) // 返回: true
 * isEmptyVal(0) // 返回: false
 */
export const isEmptyVal = (val: any): boolean => {
  return val === '' || val === null || val === undefined
}

// ========== 数值判断 ==========

/**
 * 判断数值是否为有效数字（不是 NaN）
 * @param val - 待判断的值
 * @returns 如果是有效数字返回 true
 */
export const isValidNumber = (val: unknown): val is number => {
  return isNumber(val) && !Number.isNaN(val) && Number.isFinite(val)
}

/**
 * 判断数值是否为正数
 * @param val - 待判断的值
 * @returns 如果是正数返回 true
 */
export const isPositiveNumber = (val: unknown): val is number => {
  return isValidNumber(val) && val > 0
}

/**
 * 判断数值是否为整数
 * @param val - 待判断的值
 * @returns 如果是整数返回 true
 */
export const isInteger = (val: unknown): val is number => {
  return isValidNumber(val) && Number.isInteger(val)
}
