import request from '@/config/axios'

// 设备类型柱状图
export const getDeviceStatistics = () => {
  return request.get({ url: '/infra/device-overview-info/device/statistics' })
}

// 统计设备基础信息数量
export const getBasicCount = () => {
  return request.get({ url: '/infra/device-overview-info/getBasicCount' })
}

//统计设备供应商数量
export const getSupplierCount = () => {
  return request.get({ url: '/infra/device-overview-info/getSupplierCount' })
}
//统计备件信息
export const getSpareCount = () => {
  return request.get({ url: '/infra/device-spare-info/getSpareCount' })
}
