import { FormSchema } from './types'

export const hikFormShema: FormSchema[] = [
  { label: '监控点唯一标识', prop: 'cameraIndexCode', type: 'input' },
  { label: '海拔', prop: 'altitude', type: 'input' }, //补充字段海拔
  { label: '监控点名称', prop: 'name', type: 'input' },
  {
    label: '监控点类型',
    prop: 'cameraType',
    type: 'select',
    options: [
      { label: '枪机', value: 0 },
      { label: '半球', value: 1 },
      { label: '快球', value: 2 },
      { label: '云台枪机', value: 3 }
    ]
  },
  { label: '监控点类型说明', prop: 'cameraTypeName', type: 'input' },
  { label: '能力集', prop: 'capabilitySet', type: 'input' },
  { label: '能力集说明', prop: 'capabilitySetName', type: 'input' },
  { label: '智能分析能力集', prop: 'intelligentSet', type: 'input' },
  { label: '智能分析能力集说明', prop: 'intelligentSetName', type: 'input' },
  { label: '通道编号', prop: 'channelNo', type: 'input' },
  { label: '通道类型', prop: 'channelType', type: 'input' },
  { label: '通道子类型说明', prop: 'channelTypeName', type: 'input' },
  { label: '所属编码设备唯一标识', prop: 'encodeDevIndexCode', type: 'input' },
  { label: '所属设备类型', prop: 'encodeDevResourceType', type: 'input' },
  { label: '所属设备类型说明', prop: 'encodeDevResourceTypeName', type: 'input' },
  { label: '监控点国标编码', prop: 'gbIndexCode', type: 'input' },
  { label: '安装位置', prop: 'installLocation', type: 'input' },
  { label: '键盘控制码', prop: 'keyBoardCode', type: 'input' },
  // {
  //   label: '经纬度',
  //   prop: ['longitude', 'latitude'],
  //   type: 'location'
  // },
  {
    label: '摄像机像素',
    prop: 'pixel',
    type: 'select',
    options: [
      { label: '普通像素', value: 1 },
      { label: '130万高清', value: 2 },
      { label: '200万高清', value: 3 },
      { label: '300万高清', value: 4 }
    ]
  },
  {
    label: '云镜类型',
    prop: 'ptz',
    type: 'select',
    options: [
      { label: '全方位云台', value: 1 },
      { label: '不带变焦', value: 2 },
      { label: '只转动', value: 3 },
      { label: '无云台', value: 4 }
    ]
  },
  {
    label: '云台控制码',
    prop: 'ptzController',
    type: 'select',
    options: [
      // { label: 'DVR', value: 1 },
      // { label: '模拟矩阵', value: 2 },
      // { label: 'MU4000', value: 3 },
      // { label: 'NC600', value: 4 }
      { label: '1', value: 1 },
      { label: '2', value: 2 },
      { label: '3', value: 3 },
      { label: '4', value: 4 }
    ]
  },
  { label: '云台控制说明', prop: 'ptzControllerName', type: 'input' },
  { label: '云镜类型说明', prop: 'ptzName', type: 'input' },
  {
    label: '录像存储位置',
    prop: 'recordLocation',
    type: 'select',
    options: [
      { label: '中心存储', value: '0' },
      { label: '设备存储', value: '1' }
    ]
  },
  { label: '录像存储位置说明', prop: 'recordLocationName', type: 'input' },
  { label: '所属区域唯一标识', prop: 'regionIndexCode', type: 'input' },
  {
    label: '在线状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '不在线', value: 0 },
      { label: '在线', value: 1 }
    ]
  },
  { label: '状态说明', prop: 'statusName', type: 'input' },
  {
    label: '传输协议',
    prop: 'transType',
    type: 'select',
    options: [
      { label: 'UDP', value: 0 },
      { label: 'TCP', value: 1 }
    ]
  },
  { label: '传输协议类型说明', prop: 'transTypeName', type: 'input' },
  { label: '接入协议', prop: 'treatyType', type: 'input' },
  { label: '接入协议类型说明', prop: 'treatyTypeName', type: 'input' },
  { label: '可视域相关', prop: 'viewshed', type: 'input' }
]

export const hikData = Object.fromEntries(
  hikFormShema.flatMap((item) =>
    Array.isArray(item.prop)
      ? item.prop.map((p) => [p, undefined])
      : [[item.prop, item.multiple ? [] : undefined]]
  )
)
