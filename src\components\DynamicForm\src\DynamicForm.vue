<template>
  <el-form :model="formModel" :rules="rules" ref="formRef" label-width="150px">
    <el-form-item
      v-for="item in schema"
      :key="Array.isArray(item.prop) ? item.prop.join('-') : item.prop"
      :label="item.label"
      :prop="Array.isArray(item.prop) ? item.prop[0] : item.prop"
    >
      <slot :name="`item.${Array.isArray(item.prop) ? item.prop[0] : item.prop}`">
        <component
          :is="getComponent(item)"
          :model-value="getValue(item)"
          v-bind="getComponentProps(item)"
          @update:model-value="handleComponentUpdate($event, item)"
          :multiple="!!item.multiple"
        >
          <template v-if="item.type === 'select'">
            <template v-for="option in selectOptionsMap[item.prop] || []" :key="option.value">
              <el-option
                :label="option[item?.labelKey || 'label']"
                :value="option[item?.valueKey] || option.id || option.value"
              />
            </template>
          </template>
        </component>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive, watch, toRefs, ref } from 'vue'
import { ElForm, ElInput, ElSelect, ElOption, ElDatePicker, ElTreeSelect } from 'element-plus'
import dayjs from 'dayjs'
import LocationInput from './components/LocationInput.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  schema: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref(null)
const formModel = reactive({ ...props.modelValue })

const selectOptionsMap = reactive({})
onMounted(() => {
  getOption()
})

const getOption = () => {
  props.schema.forEach(async (item) => {
    if ((item.type === 'select' || item.type === 'tree') && typeof item.api === 'function') {
      const list = await item.api()
      selectOptionsMap[item.prop] = list
    } else if (item.type === 'select' && Array.isArray(item.options)) {
      selectOptionsMap[item.prop] = item.options
    }
  })
}
// 监听 modelValue 的变化进行同步
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formModel, newVal)
  },
  { deep: true }
)

// 双向绑定
watch(
  formModel,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

// 校验规则
const rules = {}
props.schema.forEach((field) => {
  if (field.required) {
    rules[field.prop] = [{ required: true, message: `${field.label}不能为空`, trigger: 'blur' }]
  }
})

// 返回组件类型
const getComponent = (item) => {
  switch (item.type) {
    case 'input':
      return ElInput
    case 'select':
      return ElSelect
    case 'date':
      return ElDatePicker
    case 'location':
      return LocationInput
    case 'tree':
      return ElTreeSelect
    default:
      return ElInput
  }
}

// 返回组件的额外属性
const getComponentProps = (item) => {
  if (item.type === 'date') {
    return {
      type: 'date',
      valueFormat: item.timestamp ? undefined : 'YYYY-MM-DD',
      placeholder: `请选择${item.label}`
    }
  } else if (item.type === 'select') {
    return {
      placeholder: `请选择${item.label}`,
      clearable: true
    }
  } else if (item.type === 'input') {
    return {
      placeholder: `请输入${item.label}`,
      clearable: true
    }
  } else if (item.type === 'tree') {
    return {
      placeholder: `请选择${item.label}`,
      data: selectOptionsMap[item.prop] || [],
      props: {
        value: item.valueKey || 'id',
        label: item.labelKey || 'label',
        children: item.childrenKey || 'children'
      },
      checkStrictly: item.checkStrictly ?? true, // 是否严格父子关联
      clearable: true
    }
  }
  return {}
}

const getValue = (item) => {
  if (item.type === 'location' && Array.isArray(item.prop)) {
    return {
      latitude: formModel[item.prop[0]] || '',
      longitude: formModel[item.prop[1]] || ''
    }
  } else if (item.type === 'date' && item.timestamp && formModel[item.prop]) {
    return new Date(formModel[item.prop]) // ✅ 时间戳转 Date 对象
  } else {
    return formModel[item.prop]
  }
}

const handleComponentUpdate = (val, item) => {
  // debugger
  if (item.type === 'location' && Array.isArray(item.prop)) {
    formModel[item.prop[0]] = val.longitude
    formModel[item.prop[1]] = val.latitude
  } else if (item.type === 'date' && item.timestamp) {
    formModel[item.prop] = val instanceof Date ? val.getTime() : undefined
  } else {
    formModel[item.prop] = val
  }
}

const reset = () => {
  Object.keys(formModel).forEach((key) => {
    formModel[key] = ''
  })
  formRef.value?.clearValidate()
}
const validate = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate((valid, fields) => {
      if (valid) {
        resolve(true)
      } else {
        resolve({
          valid: false,
          fields
        })
      }
    })
  })
}

defineExpose({
  reset,
  validate
})
</script>
