import request from '@/config/axios'

export interface PostVO {
  deptId?: number
  month: string
  year: string
  id: string
  userId: string
  pageNo: string
  pageSize: string
  orgAccessStatus: string
  username: string
  days: string
  roleId: string
  day: any
  staffs: any

}

// 查询排班列表
export const getList = async (id: string) => {
  return await request.get({ url: '/system/omu-schedule/list?deptName=' + id })
}

// 查询排班详情
export const getDetails = async (params:PostVO) => {
  return await request.get({ url: `/system/omu-schedule/detail?deptId=${params.deptId}&month=${params.month}&year=${params.year}` })
}
// 查询排班用户列表
export const getUserList = async (params:PostVO) => {
  return await request.get({ url: `/system/user/page?deptId=${params.deptId}&pageNo=${params.pageNo}&pageSize=${params.pageSize}&orgAccessStatus=${params.orgAccessStatus}&username=${params.username}&roleId=${params.roleId}` })
}

// 删除排班人员
export const DeleteSchedule = async (params:PostVO) => {
  return await request.delete({ url: `/system/omu-schedule/delete?year=${params.year}&userId=${params.userId}&month=${params.month}&day=${params.days}` })
}

// 排班人员新增
export const addScheduling = async (data: PostVO) => {
  return await request.post({ url: '/system/omu-schedule/create', data })
}


// 导出排班
export const exportScheduling = async (params:PostVO) => {
  return await request.download({ url: `/system/omu-schedule/export-excel?deptId=${params.deptId}&month=${params.month}&year=${params.year}` })
}

//导入排班
export const DictionaryImport = async (data) => {
  return await request.upload({ url: `/system/omu-schedule/import`,data })
}
// 排班模板
export const ImportTemplate = async () => {
  return await request.download({ url: `/system/omu-schedule/get-import-template` })
}

// 查询岗位列表
export const getPostPage = async (params: PageParam) => {
  return await request.get({ url: '/system/post/page', params })
}

// 获取岗位精简信息列表
export const getSimplePostList = async (): Promise<PostVO[]> => {
  return await request.get({ url: '/system/post/simple-list' })
}
// 获取角色精简信息列表
export const getRoleList = async (): Promise<PostVO[]> => {
  return await request.get({ url: '/system/role/simple-list' })
}

// 查询岗位详情
export const getPost = async (id: number) => {
  return await request.get({ url: '/system/post/get?id=' + id })
}

// 新增岗位
export const createPost = async (data: PostVO) => {
  return await request.post({ url: '/system/post/create', data })
}

// 修改岗位
export const updatePost = async (data: PostVO) => {
  return await request.put({ url: '/system/post/update', data })
}

// 删除岗位
export const deletePost = async (id: number) => {
  return await request.delete({ url: '/system/post/delete?id=' + id })
}

// 导出岗位
export const exportPost = async (params) => {
  return await request.download({ url: '/system/post/export', params })
}
