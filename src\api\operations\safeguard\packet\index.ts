import request from '@/config/axios'

export interface BannerVO {
  groupName: string
  warnType: number
  guaranteeType: number
  timeList: Array<{
    startTime: string
    endTime: string
  }>
}

// 分页/搜索
export const getList = async (params) => {
  return await request.get({ url: `/infra/assets-group/page`, params })
}

// 添加
export const addList = async (data: BannerVO) => {
  return await request.post({ url: `/infra/assets-group/create`, data })
}

// 编辑/修改
export const updateItem = async (data: BannerVO) => {
  return await request.put({ url: `/infra/assets-group/update`, data })
}

// 删除
export const deleteList = async (params) => {
  return await request.delete({
    url: `/infra/assets-group/delete`,
    params
  })
}
