<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <style>
      .cls-1 {
        
      }

      .cls-2 {
        fill: none;
      }
    </style>
  </defs>
  <g id="运维区域列表" transform="translate(-387 -72)">
    <path id="联合_41" data-name="联合 41" class="cls-1" d="M20333.021,21285.658h-6.082a2.577,2.577,0,1,1-3.1-3.5v-6.109a2.575,2.575,0,1,1,3.227-3.107h5.873a2.575,2.575,0,1,1,3.217,3.139v6.186a2.576,2.576,0,1,1-3.137,3.395Zm.93-1.012v.172a1.48,1.48,0,1,0,0-.172Zm-10.857-.021a1.482,1.482,0,1,0,1.48-1.48A1.488,1.488,0,0,0,20323.094,21284.625Zm2.211-8.578v6.109a2.556,2.556,0,0,1,1.807,2.039h5.8a2.587,2.587,0,0,1,1.785-1.932v-6.186a2.594,2.594,0,0,1-1.721-1.676h-5.959A2.611,2.611,0,0,1,20325.3,21276.047Zm8.641-2.436a1.481,1.481,0,1,0,1.48-1.48A1.486,1.486,0,0,0,20333.945,21273.611Zm-10.852-.033a1.483,1.483,0,0,0,2.943.254v-.512a1.483,1.483,0,0,0-2.943.258Z" transform="translate(-19927 -21191)"/>
    <rect id="矩形_1091" data-name="矩形 1091" class="cls-2" width="32" height="32" transform="translate(387 72)"/>
  </g>
</svg>
