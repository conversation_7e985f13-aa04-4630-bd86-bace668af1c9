import { FormSchema } from './types'

export const zabbixFormShema: FormSchema[] = [
  { label: '主机ID（设备编码）', prop: 'hostid', type: 'input' },
  { label: 'proxy的ID', prop: 'proxyid', type: 'input' },
  { label: '主机的技术名称', prop: 'host', type: 'input' },
  {
    label: '主机状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '启用', value: '0' },
      { label: '禁用', value: '1' }
    ]
  },
  { label: 'IPMI身份验证算法', prop: 'ipmiAuthtype', type: 'input' },
  { label: 'IPMI特权级别', prop: 'ipmiPrivilege', type: 'input' },
  { label: 'IPMI用户名', prop: 'ipmiUsername', type: 'input' },
  { label: 'IPMI密码', prop: 'ipmiPassword', type: 'input' },
  { label: '有效的维护ID', prop: 'maintenanceid', type: 'input' },
  {
    label: '维护状态',
    prop: 'maintenanceStatus',
    type: 'select',
    options: [
      { label: '未维护', value: '0' },
      { label: '维护中', value: '1' }
    ]
  },
  {
    label: '维护类型',
    prop: 'maintenanceType',
    type: 'select',
    options: [
      { label: '普通', value: '0' },
      { label: '活动', value: '1' }
    ]
  },
  { label: '维护开始时间', prop: 'maintenanceFrom', type: 'date' },
  { label: '主机显示名称', prop: 'name', type: 'input' },
  { label: '主机来源', prop: 'flags', type: 'input' },
  { label: 'template ID', prop: 'templateid', type: 'input' },
  { label: '主机描述', prop: 'description', type: 'input' },
  {
    label: 'TLS连接模式',
    prop: 'tlsConnect',
    type: 'select',
    options: [
      { label: '不使用加密', value: '1' },
      { label: '使用TLS', value: '2' },
      { label: '仅PSK', value: '3' },
      { label: '仅证书', value: '4' }
    ]
  },
  {
    label: 'TLS验证方式',
    prop: 'tlsAccept',
    type: 'select',
    options: [
      { label: '无', value: '1' },
      { label: 'PSK', value: '2' },
      { label: '证书', value: '4' }
    ]
  },
  { label: '证书颁发者', prop: 'tlsIssuer', type: 'input' },
  { label: '证书主题', prop: 'tlsSubject', type: 'input' },
  { label: 'custom_interfaces', prop: 'customInterfaces', type: 'input' },
  { label: 'UUID', prop: 'uuid', type: 'input' },
  { label: 'vendor name', prop: 'vendorName', type: 'input' },
  { label: 'vendor version', prop: 'vendorVersion', type: 'input' },
  { label: 'proxy组ID', prop: 'proxyGroupid', type: 'input' },
  { label: '监控主机的来源', prop: 'monitoredBy', type: 'input' },
  {
    label: '资产填充模式',
    prop: 'inventoryMode',
    type: 'select',
    options: [
      { label: '禁用', value: '-1' },
      { label: '手动', value: '0' },
      { label: '自动', value: '1' }
    ]
  },
  {
    label: '是否可用',
    prop: 'activeAvailable',
    type: 'select',
    options: [
      { label: '否', value: '0' },
      { label: '是', value: '1' }
    ]
  },
  { label: 'assignedProxyid', prop: 'assignedProxyid', type: 'input' }
]

export const zabbixData = Object.fromEntries(
  zabbixFormShema
    .map((item) =>
      Array.isArray(item.prop)
        ? item.prop.map((p) => [p, undefined])
        : [[item.prop, item.multiple ? [] : undefined]]
    )
    .flat()
)
