<template>
  <el-cascader
    ref="cascaderRef"
    :style="{ width: width }"
    v-model="selectValue"
    :options="options"
    :props="mergedCascaderProps"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="maxCollapseTags"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { getDicByDictType } from '@/api/EquipmentManagement/ManagementCenter/index'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  dictType: {
    type: String,
    required: true
  },
  modelValue: {
    type: Array as () => Array<string | number>,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  maxCollapseTags: {
    type: Number,
    default: 3
  },
  cascaderProps: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectValue = ref<Array<any>>([])
const previousValue = ref<Array<any>>([])
const options = ref([])

const mergedCascaderProps = computed(() => ({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false,
  ...props.cascaderProps
}))

watch(
  () => props.modelValue,
  (newVal) => {
    selectValue.value = Array.isArray(newVal) ? [...newVal] : []
    previousValue.value = [...selectValue.value]
  },
  { immediate: true }
)

const getAllChildrenIds = (node: any): any[] => {
  const ids = [node.id]
  if (node.children?.length) {
    node.children.forEach((child) => {
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

const findNodeById = (nodes: any[], id: any): any => {
  for (const node of nodes) {
    if (node.id === id) return node
    if (node.children) {
      const found = findNodeById(node.children, id)
      if (found) return found
    }
  }
  return null
}

const allChildrenSelected = (node: any, selectedIds: any[]): boolean => {
  if (!node.children?.length) return false
  return node.children.every((child) =>
    child.children?.length
      ? allChildrenSelected(child, selectedIds)
      : selectedIds.includes(child.id)
  )
}

const autoSelectParent = (nodes: any[], selectedSet: Set<any>) => {
  for (const node of nodes) {
    if (node.children?.length) {
      autoSelectParent(node.children, selectedSet)
      if (allChildrenSelected(node, [...selectedSet])) {
        selectedSet.add(node.id)
      } else {
        selectedSet.delete(node.id)
      }
    }
  }
}

const cascaderRef = ref()

const handleChange = (val: any[]) => {
  // 获取滚动容器
  const panel = cascaderRef.value?.panel?.$el?.querySelector('.el-cascader-panel')
  const scrollTop = panel?.scrollTop || 0

  const added = val.filter((v) => !previousValue.value.includes(v))
  const removed = previousValue.value.filter((v) => !val.includes(v))
  const selectedSet = new Set(val)

  for (const id of added) {
    const node = findNodeById(options.value, id)
    if (node) getAllChildrenIds(node).forEach((childId) => selectedSet.add(childId))
  }

  for (const id of removed) {
    const node = findNodeById(options.value, id)
    if (node) getAllChildrenIds(node).forEach((childId) => selectedSet.delete(childId))
  }

  autoSelectParent(options.value, selectedSet)

  const result = [...selectedSet]
  selectValue.value = result
  previousValue.value = [...result]
  emit('update:modelValue', result)
  emit('change', result)

  nextTick(() => {
    if (panel) panel.scrollTop = scrollTop
  })
}

const loadDictData = async () => {
  try {
    options.value = await getDicByDictType(props.dictType)
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

onMounted(loadDictData)
</script>
