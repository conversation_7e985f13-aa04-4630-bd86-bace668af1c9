import request from '@/config/axios'
// 分页搜索
export const getWarnList = async (params) => {
  return await request.get({ url: `/infra/warn-abnormal-config/page`, params })
}
// 新增
export const postAdd = async (data) => {
  return await request.post({ url: '/infra/warn-abnormal-config/create', data })
}
// 删除
export const deleteWarnConfiguration = async (params) => {
  return await request.delete({ url: '/infra/warn-abnormal-config/delete', params })
}

// 启用
export const getOpen = async (params) => {
  return await request.get({ url: '/infra/warn-abnormal-config/open', params })
}

// 关闭
export const getClose = async (params) => {
  return await request.get({ url: '/infra/warn-abnormal-config/close', params })
}
